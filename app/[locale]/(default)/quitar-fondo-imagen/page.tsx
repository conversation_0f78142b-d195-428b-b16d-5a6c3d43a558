import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import RemoveBackgroundPage from "@/components/pages/remove-background";

interface Props {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "metadata" });
  
  return {
    title: t("remove_background.title"),
    description: t("remove_background.description"),
    keywords: t("remove_background.keywords"),
    openGraph: {
      title: t("remove_background.title"),
      description: t("remove_background.description"),
      type: "website",
    },
  };
}

export default async function Page({ params }: Props) {
  const { locale } = await params;
  
  // Load page translations
  const pageTranslations = await import(`@/i18n/pages/remove-background/${locale}.json`).catch(() => 
    import(`@/i18n/pages/remove-background/en.json`)
  );
  
  return <RemoveBackgroundPage data={pageTranslations.default} locale={locale} />;
}
