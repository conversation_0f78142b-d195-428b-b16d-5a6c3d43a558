import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import ImageEnhancerPage from "@/components/pages/image-enhancer";

interface Props {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "metadata" });

  return {
    title: t("image_enhancer.title"),
    description: t("image_enhancer.description"),
    keywords: t("image_enhancer.keywords"),
    openGraph: {
      title: t("image_enhancer.title"),
      description: t("image_enhancer.description"),
      type: "website",
    },
  };
}

export default async function Page({ params }: Props) {
  const { locale } = await params;

  // Load page translations
  const pageTranslations = await import(`@/i18n/pages/image-enhancer/${locale}.json`).catch(() =>
    import(`@/i18n/pages/image-enhancer/en.json`)
  );

  return <ImageEnhancerPage data={pageTranslations.default} locale={locale} />;
}
