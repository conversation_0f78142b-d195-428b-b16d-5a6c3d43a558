import { getTranslations } from "next-intl/server";

export async function getPageData(page: string, locale: string) {
  const t = await getTranslations({ locale, namespace: "pages" });
  
  // Common header and footer data
  const commonData = {
    header: {
      brand: {
        title: "Circle Crop Image",
        logo: { src: "/logo.svg", alt: "Circle Crop Image" },
        url: `/${locale}`
      },
      nav: {
        items: [
          {
            title: t("nav.circle_crop"),
            url: `/${locale}`,
            target: "_self"
          },
          {
            title: t("nav.image_enhancer"),
            url: `/${locale}/image-enhancer`,
            target: "_self"
          }
        ]
      },
      show_locale: true,
      show_theme: true
    },
    footer: {
      brand: {
        title: "Circle Crop Image",
        description: t("footer.description"),
        logo: { src: "/logo.svg", alt: "Circle Crop Image" },
        url: `/${locale}`
      },
      copyright: t("footer.copyright"),
      nav: {
        items: [
          {
            title: t("footer.nav.tools"),
            children: [
              {
                title: t("footer.nav.circle_crop"),
                url: `/${locale}#upload`,
                target: "_self"
              },
              {
                title: t("footer.nav.image_enhancer"),
                url: `/${locale}/image-enhancer`,
                target: "_self"
              }
            ]
          }
        ]
      },
      agreement: {
        items: [
          {
            title: t("footer.agreement.privacy"),
            url: "/privacy-policy"
          },
          {
            title: t("footer.agreement.terms"),
            url: "/terms-of-service"
          },
          {
            title: t("footer.agreement.contact"),
            url: "/contact"
          }
        ]
      },
      friends: {
        title: t("footer.friends.title"),
        items: [
          {
            title: t("footer.friends.password_generator"),
            url: "https://generatepassword12.org/",
            target: "_blank",
            description: t("footer.friends.password_description")
          },
          {
            title: t("footer.friends.name_generator"),
            url: "https://name-generator.app/",
            target: "_blank",
            description: t("footer.friends.name_description")
          },
          {
            title: t("footer.friends.cv_builder"),
            url: "https://cvbuilderfree.net/",
            target: "_blank",
            description: t("footer.friends.cv_description")
          }
        ]
      }
    }
  };

  if (page === "image-enhancer") {
    return {
      ...commonData,
      hero: {
        title: t("image_enhancer.hero.title"),
        description: t("image_enhancer.hero.description"),
        buttons: [
          {
            title: t("image_enhancer.hero.button"),
            url: "#upload",
            target: "_self",
            variant: "default",
            icon: "HiOutlineCloudArrowUp"
          }
        ],
        tip: t("image_enhancer.hero.tip")
      },
      feature: {
        title: t("image_enhancer.features.title"),
        description: t("image_enhancer.features.description"),
        items: [
          {
            title: t("image_enhancer.features.brightness.title"),
            description: t("image_enhancer.features.brightness.description"),
            icon: "HiOutlineSun"
          },
          {
            title: t("image_enhancer.features.contrast.title"),
            description: t("image_enhancer.features.contrast.description"),
            icon: "HiOutlineAdjustmentsHorizontal"
          },
          {
            title: t("image_enhancer.features.sharpness.title"),
            description: t("image_enhancer.features.sharpness.description"),
            icon: "HiOutlineSparkles"
          },
          {
            title: t("image_enhancer.features.denoise.title"),
            description: t("image_enhancer.features.denoise.description"),
            icon: "HiOutlineShieldCheck"
          },
          {
            title: t("image_enhancer.features.realtime.title"),
            description: t("image_enhancer.features.realtime.description"),
            icon: "HiOutlineBolt"
          },
          {
            title: t("image_enhancer.features.privacy.title"),
            description: t("image_enhancer.features.privacy.description"),
            icon: "HiOutlineLockClosed"
          }
        ]
      }
    };
  }

  return commonData;
}
