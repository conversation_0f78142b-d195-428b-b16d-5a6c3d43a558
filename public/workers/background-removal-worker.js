// Background Removal Web Worker
// This worker handles heavy image processing in the background thread

let tf, bodyPix, model;

// Initialize TensorFlow.js and BodyPix
async function initializeModels() {
  try {
    // Load TensorFlow.js from CDN
    importScripts('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.22.0/dist/tf.min.js');
    tf = self.tf;

    // Load BodyPix from CDN
    importScripts('https://cdn.jsdelivr.net/npm/@tensorflow-models/body-pix@2.2.1/dist/body-pix.min.js');
    bodyPix = self.bodyPix;
    
    // Set TensorFlow.js backend to WebGL for better performance
    await tf.setBackend('webgl');
    await tf.ready();
    
    // Load the BodyPix model with optimized settings
    model = await bodyPix.load({
      architecture: 'MobileNetV1',
      outputStride: 16,
      multiplier: 0.75,
      quantBytes: 2,
    });
    
    self.postMessage({
      type: 'MODEL_LOADED',
      success: true
    });
    
  } catch (error) {
    self.postMessage({
      type: 'MODEL_LOADED',
      success: false,
      error: error.message
    });
  }
}

// Process image for background removal
async function processImage(imageData, options = {}) {
  try {
    if (!model) {
      throw new Error('Model not loaded');
    }
    
    const {
      segmentationThreshold = 0.7,
      enablePostProcessing = true,
      method = 'person' // 'person' or 'parts'
    } = options;
    
    // Update progress
    self.postMessage({
      type: 'PROGRESS',
      progress: 20
    });
    
    // Create canvas from image data
    const canvas = new OffscreenCanvas(imageData.width, imageData.height);
    const ctx = canvas.getContext('2d');
    ctx.putImageData(imageData, 0, 0);
    
    // Update progress
    self.postMessage({
      type: 'PROGRESS',
      progress: 40
    });
    
    // Perform segmentation
    let segmentation;
    if (method === 'person') {
      segmentation = await model.segmentPerson(canvas, {
        flipHorizontal: false,
        internalResolution: 'high',
        segmentationThreshold: segmentationThreshold,
        maxDetections: 5,
        scoreThreshold: 0.3,
        nmsRadius: 20,
      });
    } else {
      segmentation = await model.segmentPersonParts(canvas, {
        flipHorizontal: false,
        internalResolution: 'high',
        segmentationThreshold: segmentationThreshold,
        maxDetections: 5,
        scoreThreshold: 0.3,
        nmsRadius: 20,
      });
    }
    
    // Update progress
    self.postMessage({
      type: 'PROGRESS',
      progress: 70
    });
    
    // Apply segmentation mask
    const outputImageData = new ImageData(
      new Uint8ClampedArray(imageData.data),
      imageData.width,
      imageData.height
    );
    
    // Process segmentation data
    for (let i = 0; i < segmentation.data.length; i++) {
      const isPerson = segmentation.data[i];
      if (!isPerson) {
        // Set alpha to 0 for background pixels
        outputImageData.data[i * 4 + 3] = 0;
      } else {
        // Ensure person pixels are fully opaque
        outputImageData.data[i * 4 + 3] = 255;
      }
    }
    
    // Update progress
    self.postMessage({
      type: 'PROGRESS',
      progress: 85
    });
    
    // Post-processing for smoother edges
    if (enablePostProcessing) {
      applyEdgeSmoothing(outputImageData);
    }
    
    // Update progress
    self.postMessage({
      type: 'PROGRESS',
      progress: 100
    });
    
    // Send result back to main thread
    self.postMessage({
      type: 'PROCESSING_COMPLETE',
      imageData: outputImageData,
      success: true
    });
    
  } catch (error) {
    self.postMessage({
      type: 'PROCESSING_COMPLETE',
      success: false,
      error: error.message
    });
  }
}

// Apply edge smoothing for better results
function applyEdgeSmoothing(imageData) {
  const { data, width, height } = imageData;
  const newData = new Uint8ClampedArray(data);
  
  // Apply Gaussian blur to alpha channel for smoother edges
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      const alpha = data[idx + 3];
      
      if (alpha > 0 && alpha < 255) {
        // Smooth alpha edges
        let alphaSum = 0;
        let count = 0;
        
        // 3x3 kernel
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            alphaSum += data[neighborIdx + 3];
            count++;
          }
        }
        
        newData[idx + 3] = Math.round(alphaSum / count);
      }
    }
  }
  
  // Copy smoothed alpha values back
  for (let i = 3; i < data.length; i += 4) {
    data[i] = newData[i];
  }
}

// Handle messages from main thread
self.onmessage = async function(e) {
  const { type, data } = e.data;
  
  switch (type) {
    case 'INITIALIZE':
      await initializeModels();
      break;
      
    case 'PROCESS_IMAGE':
      await processImage(data.imageData, data.options);
      break;
      
    case 'CLEANUP':
      // Clean up resources
      if (model) {
        model.dispose();
        model = null;
      }
      if (tf) {
        tf.disposeVariables();
      }
      self.postMessage({
        type: 'CLEANUP_COMPLETE'
      });
      break;
      
    default:
      console.warn('Unknown message type:', type);
  }
};
