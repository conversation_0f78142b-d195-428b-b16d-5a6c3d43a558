"use client";

import React from "react";
import { useTranslations } from "next-intl";
import RemoveBackgroundTool from "@/components/blocks/remove-background-tool";

interface RemoveBackgroundPageProps {
  data: any;
  locale: string;
}

export default function RemoveBackgroundPage({ data, locale }: RemoveBackgroundPageProps) {
  const t = useTranslations();

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            {data.remove_background.hero.title}
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            {data.remove_background.hero.description}
          </p>
          <div className="flex justify-center mb-4">
            <a
              href="#upload"
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              {data.remove_background.hero.button}
            </a>
          </div>
          <p className="text-sm text-muted-foreground">
            {data.remove_background.hero.tip}
          </p>
        </div>
      </section>

      {/* Remove Background Tool */}
      <section id="upload" className="py-12 lg:py-16">
        <div className="container mx-auto px-4">
          <RemoveBackgroundTool />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-24 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              {data.remove_background.features.title}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {data.remove_background.features.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Object.entries(data.remove_background.features).filter(([key]) => 
              !['title', 'description'].includes(key)
            ).map(([key, feature]: [string, any]) => (
              <div key={key} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              {data.remove_background.how_it_works.title}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {data.remove_background.how_it_works.description}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {data.remove_background.how_it_works.steps.map((step: any, index: number) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {index + 1}
                  </span>
                </div>
                <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 lg:py-24 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              {data.remove_background.faq.title}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {data.remove_background.faq.description}
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto space-y-6">
            {Object.entries(data.remove_background.faq).filter(([key]) => 
              !['title', 'description'].includes(key)
            ).map(([key, faq]: [string, any]) => (
              <div key={key} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-3">{faq.title}</h3>
                <p className="text-muted-foreground">{faq.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-green-600 to-emerald-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            {data.remove_background.cta.title}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            {data.remove_background.cta.description}
          </p>
          <a
            href="#upload"
            className="inline-flex items-center px-8 py-4 bg-white text-green-600 rounded-lg hover:bg-gray-100 transition-colors font-semibold"
          >
            {data.remove_background.cta.button}
          </a>
        </div>
      </section>
    </div>
  );
}
