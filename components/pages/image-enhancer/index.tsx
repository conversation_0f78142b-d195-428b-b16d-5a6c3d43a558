"use client";

import React from "react";
import { useTranslations } from "next-intl";
import Header from "@/components/blocks/header";
import Footer from "@/components/blocks/footer";
import ImageEnhancerTool from "@/components/blocks/image-enhancer-tool";

interface ImageEnhancerPageProps {
  data: any;
  locale: string;
}

export default function ImageEnhancerPage({ data, locale }: ImageEnhancerPageProps) {
  const t = useTranslations();

  // Create header data
  const headerData = {
    disabled: false,
    name: "header",
    brand: {
      title: "Circle Crop Image",
      logo: { src: "/logo.svg", alt: "Circle Crop Image" },
      url: `/${locale}`
    },
    nav: {
      items: [
        {
          title: data.nav.circle_crop,
          url: `/${locale}`,
          target: "_self",
          icon: "HiOutlinePhoto"
        },
        {
          title: data.nav.image_enhancer,
          url: `/${locale}/image-enhancer`,
          target: "_self",
          icon: "HiOutlineSparkles"
        }
      ]
    },
    buttons: [],
    show_sign: false,
    show_locale: true,
    show_theme: true
  };

  // Create footer data
  const footerData = {
    disabled: false,
    name: "footer",
    brand: {
      title: "Circle Crop Image",
      description: data.footer.description,
      logo: { src: "/logo.svg", alt: "Circle Crop Image" },
      url: `/${locale}`
    },
    copyright: data.footer.copyright,
    nav: {
      items: [
        {
          title: data.footer.nav.tools,
          children: [
            {
              title: data.footer.nav.circle_crop,
              url: `/${locale}#upload`,
              target: "_self"
            },
            {
              title: data.footer.nav.image_enhancer,
              url: `/${locale}/image-enhancer`,
              target: "_self"
            }
          ]
        }
      ]
    },
    agreement: {
      items: [
        {
          title: data.footer.agreement.privacy,
          url: "/privacy-policy"
        },
        {
          title: data.footer.agreement.terms,
          url: "/terms-of-service"
        },
        {
          title: data.footer.agreement.contact,
          url: "/contact"
        }
      ]
    },
    friends: {
      title: data.footer.friends.title,
      items: [
        {
          title: data.footer.friends.password_generator,
          url: "https://generatepassword12.org/",
          target: "_blank",
          description: data.footer.friends.password_description
        },
        {
          title: data.footer.friends.name_generator,
          url: "https://name-generator.app/",
          target: "_blank",
          description: data.footer.friends.name_description
        },
        {
          title: data.footer.friends.cv_builder,
          url: "https://cvbuilderfree.net/",
          target: "_blank",
          description: data.footer.friends.cv_description
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header header={headerData} />

      {/* Hero Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {data.image_enhancer.hero.title}
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            {data.image_enhancer.hero.description}
          </p>
          <div className="flex justify-center mb-4">
            <a
              href="#upload"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {data.image_enhancer.hero.button}
            </a>
          </div>
          <p className="text-sm text-muted-foreground">
            {data.image_enhancer.hero.tip}
          </p>
        </div>
      </section>

      {/* Image Enhancer Tool */}
      <section id="upload" className="py-12 lg:py-16">
        <div className="container mx-auto px-4">
          <ImageEnhancerTool />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 lg:py-24 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              {data.image_enhancer.features.title}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {data.image_enhancer.features.description}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Object.entries(data.image_enhancer.features).filter(([key]) =>
              !['title', 'description'].includes(key)
            ).map(([key, feature]: [string, any]) => (
              <div key={key} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              {data.image_enhancer.faq.title}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              {data.image_enhancer.faq.description}
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            {Object.entries(data.image_enhancer.faq).filter(([key]) =>
              !['title', 'description'].includes(key)
            ).map(([key, faq]: [string, any]) => (
              <div key={key} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
                <h3 className="text-lg font-semibold mb-3">{faq.title}</h3>
                <p className="text-muted-foreground">{faq.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 lg:py-24 bg-gradient-to-br from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            {data.image_enhancer.cta.title}
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            {data.image_enhancer.cta.description}
          </p>
          <a
            href="#upload"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors font-semibold"
          >
            {data.image_enhancer.cta.button}
          </a>
        </div>
      </section>

      {/* Footer */}
      <Footer footer={footerData} />
    </div>
  );
}
