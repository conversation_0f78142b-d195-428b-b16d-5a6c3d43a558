"use client";

import React from "react";

// Color Filter Background Removal
export const processColorFilter = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    threshold?: number;
    colorTolerance?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { threshold = 240, colorTolerance = 15 } = options;

    canvas.width = image.width;
    canvas.height = image.height;
    
    // Draw original image
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Enhanced color filtering algorithm
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // Check for white/light backgrounds
      const isWhiteBackground = r > threshold && g > threshold && b > threshold;
      
      // Check for uniform color backgrounds
      const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
      const isUniformColor = colorVariance < colorTolerance && (r + g + b) / 3 > threshold - 50;
      
      // Check for green screen (chroma key)
      const isGreenScreen = g > r + 50 && g > b + 50 && g > 100;
      
      if (isWhiteBackground || isUniformColor || isGreenScreen) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    setTimeout(() => resolve(canvas), 100);
  });
};

// Edge Detection Background Removal
export const processEdgeDetection = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    edgeThreshold?: number;
    blurRadius?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { edgeThreshold = 30, blurRadius = 2 } = options;

    canvas.width = image.width;
    canvas.height = image.height;
    
    // Draw original image
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;
    
    // Create edge detection mask
    const edgeMask = new Uint8Array(width * height);
    
    // Sobel edge detection
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        
        // Get surrounding pixels
        const tl = data[((y - 1) * width + (x - 1)) * 4]; // top-left
        const tm = data[((y - 1) * width + x) * 4];       // top-middle
        const tr = data[((y - 1) * width + (x + 1)) * 4]; // top-right
        const ml = data[(y * width + (x - 1)) * 4];       // middle-left
        const mr = data[(y * width + (x + 1)) * 4];       // middle-right
        const bl = data[((y + 1) * width + (x - 1)) * 4]; // bottom-left
        const bm = data[((y + 1) * width + x) * 4];       // bottom-middle
        const br = data[((y + 1) * width + (x + 1)) * 4]; // bottom-right
        
        // Sobel X and Y gradients
        const sobelX = (tr + 2 * mr + br) - (tl + 2 * ml + bl);
        const sobelY = (bl + 2 * bm + br) - (tl + 2 * tm + tr);
        
        // Edge magnitude
        const edgeMagnitude = Math.sqrt(sobelX * sobelX + sobelY * sobelY);
        
        edgeMask[y * width + x] = edgeMagnitude > edgeThreshold ? 1 : 0;
      }
    }
    
    // Apply edge-based background removal
    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = Math.floor(i / 4);
      const x = pixelIndex % width;
      const y = Math.floor(pixelIndex / width);
      
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // Calculate luminance
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
      
      // Check if pixel is likely background
      const isEdge = edgeMask[pixelIndex];
      const isUniform = Math.abs(r - g) < 20 && Math.abs(g - b) < 20;
      const isBright = luminance > 200;
      const isDark = luminance < 50;
      
      // Remove background if it's uniform and bright/dark, or if it's not an edge
      if ((isUniform && (isBright || isDark)) || (!isEdge && isBright)) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    setTimeout(() => resolve(canvas), 500);
  });
};

// Advanced Color Analysis Background Removal
export const processAdvancedColorAnalysis = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    sampleSize?: number;
    threshold?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { sampleSize = 20, threshold = 0.8 } = options;

    canvas.width = image.width;
    canvas.height = image.height;
    
    // Draw original image
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;
    
    // Sample edge pixels to determine background color
    const edgeColors: number[][] = [];
    
    // Sample from edges
    for (let i = 0; i < sampleSize; i++) {
      // Top edge
      const topIdx = i * Math.floor(width / sampleSize) * 4;
      if (topIdx < data.length) {
        edgeColors.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      }
      
      // Bottom edge
      const bottomIdx = ((height - 1) * width + i * Math.floor(width / sampleSize)) * 4;
      if (bottomIdx < data.length) {
        edgeColors.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
      }
      
      // Left edge
      const leftIdx = (i * Math.floor(height / sampleSize) * width) * 4;
      if (leftIdx < data.length) {
        edgeColors.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      }
      
      // Right edge
      const rightIdx = (i * Math.floor(height / sampleSize) * width + width - 1) * 4;
      if (rightIdx < data.length) {
        edgeColors.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
      }
    }
    
    // Find dominant background color
    const colorCounts = new Map<string, number>();
    edgeColors.forEach(([r, g, b]) => {
      const colorKey = `${Math.floor(r / 10)}-${Math.floor(g / 10)}-${Math.floor(b / 10)}`;
      colorCounts.set(colorKey, (colorCounts.get(colorKey) || 0) + 1);
    });
    
    const dominantColor = Array.from(colorCounts.entries())
      .sort((a, b) => b[1] - a[1])[0];
    
    if (dominantColor) {
      const [rRange, gRange, bRange] = dominantColor[0].split('-').map(Number);
      
      // Remove pixels similar to dominant background color
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        const rSimilar = Math.abs(Math.floor(r / 10) - rRange) <= 2;
        const gSimilar = Math.abs(Math.floor(g / 10) - gRange) <= 2;
        const bSimilar = Math.abs(Math.floor(b / 10) - bRange) <= 2;
        
        if (rSimilar && gSimilar && bSimilar) {
          data[i + 3] = 0; // Set alpha to 0 (transparent)
        }
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    setTimeout(() => resolve(canvas), 800);
  });
};
