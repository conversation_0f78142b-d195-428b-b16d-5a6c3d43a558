"use client";

import React from "react";

// Enhanced Color Filter Background Removal
export const processColorFilter = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    threshold?: number;
    colorTolerance?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { threshold = 220, colorTolerance = 25 } = options;

    canvas.width = image.width;
    canvas.height = image.height;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Sample edge pixels to determine background colors
    const edgeColors: number[][] = [];
    const sampleSize = 10;

    // Sample from all four edges
    for (let i = 0; i < sampleSize; i++) {
      // Top edge
      const topIdx = i * Math.floor(width / sampleSize) * 4;
      if (topIdx < data.length) {
        edgeColors.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      }

      // Bottom edge
      const bottomIdx = ((height - 1) * width + i * Math.floor(width / sampleSize)) * 4;
      if (bottomIdx < data.length) {
        edgeColors.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
      }

      // Left edge
      const leftIdx = (i * Math.floor(height / sampleSize) * width) * 4;
      if (leftIdx < data.length) {
        edgeColors.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      }

      // Right edge
      const rightIdx = (i * Math.floor(height / sampleSize) * width + width - 1) * 4;
      if (rightIdx < data.length) {
        edgeColors.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
      }
    }

    // Find dominant background colors
    const backgroundColors = edgeColors.filter(([r, g, b]) => {
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
      return luminance > 180 || luminance < 60; // Very bright or very dark
    });

    // Enhanced color filtering algorithm
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

      let shouldRemove = false;

      // Check against sampled background colors
      for (const [bgR, bgG, bgB] of backgroundColors) {
        const colorDistance = Math.sqrt(
          Math.pow(r - bgR, 2) + Math.pow(g - bgG, 2) + Math.pow(b - bgB, 2)
        );
        if (colorDistance < colorTolerance) {
          shouldRemove = true;
          break;
        }
      }

      // Check for common background patterns
      if (!shouldRemove) {
        // Very bright backgrounds
        const isVeryBright = r > threshold && g > threshold && b > threshold;

        // Very uniform colors
        const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
        const isUniformBright = colorVariance < 15 && luminance > 200;

        // Green screen detection
        const isGreenScreen = g > r + 40 && g > b + 40 && g > 120;

        // Blue screen detection
        const isBlueScreen = b > r + 40 && b > g + 40 && b > 120;

        // Gray backgrounds
        const isGrayBackground = colorVariance < 20 && luminance > 180 && luminance < 240;

        shouldRemove = isVeryBright || isUniformBright || isGreenScreen || isBlueScreen || isGrayBackground;
      }

      if (shouldRemove) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);

    setTimeout(() => resolve(canvas), 100);
  });
};

// Enhanced Edge Detection Background Removal
export const processEdgeDetection = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    edgeThreshold?: number;
    backgroundThreshold?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { edgeThreshold = 25, backgroundThreshold = 0.3 } = options;

    canvas.width = image.width;
    canvas.height = image.height;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Create edge detection mask and gradient map
    const edgeMask = new Float32Array(width * height);
    const gradientMap = new Float32Array(width * height);

    // Enhanced Sobel edge detection with multiple kernels
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;

        // Get surrounding pixels (using luminance)
        const getPixelLuminance = (px: number, py: number) => {
          const pixelIdx = (py * width + px) * 4;
          const r = data[pixelIdx];
          const g = data[pixelIdx + 1];
          const b = data[pixelIdx + 2];
          return 0.299 * r + 0.587 * g + 0.114 * b;
        };

        const tl = getPixelLuminance(x - 1, y - 1);
        const tm = getPixelLuminance(x, y - 1);
        const tr = getPixelLuminance(x + 1, y - 1);
        const ml = getPixelLuminance(x - 1, y);
        const center = getPixelLuminance(x, y);
        const mr = getPixelLuminance(x + 1, y);
        const bl = getPixelLuminance(x - 1, y + 1);
        const bm = getPixelLuminance(x, y + 1);
        const br = getPixelLuminance(x + 1, y + 1);

        // Sobel X and Y gradients
        const sobelX = (tr + 2 * mr + br) - (tl + 2 * ml + bl);
        const sobelY = (bl + 2 * bm + br) - (tl + 2 * tm + tr);

        // Laplacian for additional edge detection
        const laplacian = Math.abs(8 * center - (tl + tm + tr + ml + mr + bl + bm + br));

        // Combined edge magnitude
        const edgeMagnitude = Math.sqrt(sobelX * sobelX + sobelY * sobelY) + laplacian * 0.3;

        edgeMask[idx] = edgeMagnitude > edgeThreshold ? 1 : 0;
        gradientMap[idx] = edgeMagnitude;
      }
    }

    // Morphological operations to clean up edge mask
    const cleanedEdgeMask = new Float32Array(width * height);
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;

        // Count edge pixels in 3x3 neighborhood
        let edgeCount = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = (y + dy) * width + (x + dx);
            if (edgeMask[neighborIdx] > 0) edgeCount++;
          }
        }

        // Keep edge if it has enough neighbors (reduces noise)
        cleanedEdgeMask[idx] = edgeCount >= 3 ? 1 : 0;
      }
    }

    // Apply enhanced edge-based background removal
    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = Math.floor(i / 4);
      const x = pixelIndex % width;
      const y = Math.floor(pixelIndex / width);

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

      // Check if pixel is likely background
      const isEdge = cleanedEdgeMask[pixelIndex] > 0;
      const gradient = gradientMap[pixelIndex] || 0;

      // Color uniformity check
      const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
      const isUniform = colorVariance < 25;

      // Background likelihood factors
      const isBright = luminance > 200;
      const isVeryBright = luminance > 230;
      const isDark = luminance < 60;
      const isLowGradient = gradient < edgeThreshold * 0.5;

      // Distance from edges (simple approximation)
      let distanceFromEdge = 0;
      const searchRadius = 5;
      for (let dy = -searchRadius; dy <= searchRadius && distanceFromEdge === 0; dy++) {
        for (let dx = -searchRadius; dx <= searchRadius && distanceFromEdge === 0; dx++) {
          const checkY = y + dy;
          const checkX = x + dx;
          if (checkY >= 0 && checkY < height && checkX >= 0 && checkX < width) {
            const checkIdx = checkY * width + checkX;
            if (cleanedEdgeMask[checkIdx] > 0) {
              distanceFromEdge = Math.sqrt(dx * dx + dy * dy);
            }
          }
        }
      }

      // Decision logic for background removal
      let shouldRemove = false;

      if (isVeryBright && isUniform) {
        shouldRemove = true; // Very bright uniform areas
      } else if (!isEdge && isBright && isLowGradient) {
        shouldRemove = true; // Bright areas with low gradient and no edges
      } else if (isUniform && (isBright || isDark) && distanceFromEdge > 3) {
        shouldRemove = true; // Uniform areas far from edges
      } else if (luminance > 210 && colorVariance < 15 && isLowGradient) {
        shouldRemove = true; // Very uniform bright areas
      }

      if (shouldRemove) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);

    setTimeout(() => resolve(canvas), 500);
  });
};

// Advanced Color Analysis Background Removal
export const processAdvancedColorAnalysis = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    sampleSize?: number;
    threshold?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { sampleSize = 20, threshold = 0.8 } = options;

    canvas.width = image.width;
    canvas.height = image.height;
    
    // Draw original image
    ctx.drawImage(image, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;
    
    // Sample edge pixels to determine background color
    const edgeColors: number[][] = [];
    
    // Sample from edges
    for (let i = 0; i < sampleSize; i++) {
      // Top edge
      const topIdx = i * Math.floor(width / sampleSize) * 4;
      if (topIdx < data.length) {
        edgeColors.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      }
      
      // Bottom edge
      const bottomIdx = ((height - 1) * width + i * Math.floor(width / sampleSize)) * 4;
      if (bottomIdx < data.length) {
        edgeColors.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
      }
      
      // Left edge
      const leftIdx = (i * Math.floor(height / sampleSize) * width) * 4;
      if (leftIdx < data.length) {
        edgeColors.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      }
      
      // Right edge
      const rightIdx = (i * Math.floor(height / sampleSize) * width + width - 1) * 4;
      if (rightIdx < data.length) {
        edgeColors.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
      }
    }
    
    // Find dominant background color
    const colorCounts = new Map<string, number>();
    edgeColors.forEach(([r, g, b]) => {
      const colorKey = `${Math.floor(r / 10)}-${Math.floor(g / 10)}-${Math.floor(b / 10)}`;
      colorCounts.set(colorKey, (colorCounts.get(colorKey) || 0) + 1);
    });
    
    const dominantColor = Array.from(colorCounts.entries())
      .sort((a, b) => b[1] - a[1])[0];
    
    if (dominantColor) {
      const [rRange, gRange, bRange] = dominantColor[0].split('-').map(Number);
      
      // Remove pixels similar to dominant background color
      for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        
        const rSimilar = Math.abs(Math.floor(r / 10) - rRange) <= 2;
        const gSimilar = Math.abs(Math.floor(g / 10) - gRange) <= 2;
        const bSimilar = Math.abs(Math.floor(b / 10) - bRange) <= 2;
        
        if (rSimilar && gSimilar && bSimilar) {
          data[i + 3] = 0; // Set alpha to 0 (transparent)
        }
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    setTimeout(() => resolve(canvas), 800);
  });
};

// Post-processing utilities
const applyMorphologicalOperations = (
  imageData: ImageData,
  operation: 'erode' | 'dilate' | 'smooth'
): ImageData => {
  const { data, width, height } = imageData;
  const newData = new Uint8ClampedArray(data);

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      const alpha = data[idx + 3];

      if (operation === 'smooth' && alpha > 0 && alpha < 255) {
        // Smooth alpha edges
        let alphaSum = 0;
        let count = 0;

        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            alphaSum += data[neighborIdx + 3];
            count++;
          }
        }

        newData[idx + 3] = Math.round(alphaSum / count);
      } else if (operation === 'erode' && alpha > 0) {
        // Erode edges (make transparent areas larger)
        let minAlpha = 255;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            minAlpha = Math.min(minAlpha, data[neighborIdx + 3]);
          }
        }
        newData[idx + 3] = minAlpha;
      } else if (operation === 'dilate' && alpha === 0) {
        // Dilate edges (make opaque areas larger)
        let maxAlpha = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            maxAlpha = Math.max(maxAlpha, data[neighborIdx + 3]);
          }
        }
        if (maxAlpha > 128) {
          newData[idx + 3] = maxAlpha;
        }
      }
    }
  }

  return new ImageData(newData, width, height);
};

const applyEdgeSmoothing = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

  // Apply smoothing operations
  let smoothedData = applyMorphologicalOperations(imageData, 'smooth');
  smoothedData = applyMorphologicalOperations(smoothedData, 'smooth');

  ctx.putImageData(smoothedData, 0, 0);
};

const applyAdvancedEdgeSmoothing = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const { data, width, height } = imageData;
  const newData = new Uint8ClampedArray(data);

  // Apply Gaussian blur to alpha channel for smoother edges
  const kernel = [
    [1, 2, 1],
    [2, 4, 2],
    [1, 2, 1]
  ];
  const kernelSum = 16;

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      const alpha = data[idx + 3];

      // Only smooth edge pixels (partial transparency)
      if (alpha > 0 && alpha < 255) {
        let alphaSum = 0;

        // Apply Gaussian kernel
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const neighborIdx = ((y + ky) * width + (x + kx)) * 4;
            const weight = kernel[ky + 1][kx + 1];
            alphaSum += data[neighborIdx + 3] * weight;
          }
        }

        newData[idx + 3] = Math.round(alphaSum / kernelSum);
      }
    }
  }

  // Apply feathering for very smooth edges
  for (let y = 2; y < height - 2; y++) {
    for (let x = 2; x < width - 2; x++) {
      const idx = (y * width + x) * 4;
      const alpha = newData[idx + 3];

      if (alpha > 0 && alpha < 255) {
        // Check surrounding pixels for additional smoothing
        let edgePixels = 0;
        let totalAlpha = 0;

        for (let dy = -2; dy <= 2; dy++) {
          for (let dx = -2; dx <= 2; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            const neighborAlpha = newData[neighborIdx + 3];
            if (neighborAlpha > 0) {
              edgePixels++;
              totalAlpha += neighborAlpha;
            }
          }
        }

        if (edgePixels > 0) {
          const avgAlpha = totalAlpha / edgePixels;
          newData[idx + 3] = Math.round((alpha + avgAlpha) / 2);
        }
      }
    }
  }

  // Copy smoothed alpha values back
  for (let i = 3; i < data.length; i += 4) {
    data[i] = newData[i];
  }

  ctx.putImageData(imageData, 0, 0);
};

// Advanced WebAssembly-based Background Removal
export const processWasmAIModel = async (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    segmentationThreshold?: number;
    enablePostProcessing?: boolean;
    method?: 'person' | 'parts';
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve, reject) => {
    // This will be handled by the WasmBackgroundRemover component
    // For now, fallback to the regular AI model
    processAIModel(image, canvas, options).then(resolve).catch(reject);
  });
};

// Enhanced AI-based Background Removal using TensorFlow.js
export const processAIModel = async (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    modelType?: 'bodyPix' | 'deeplab';
    segmentationThreshold?: number;
    enablePostProcessing?: boolean;
  } = {}
): Promise<HTMLCanvasElement> => {
  const {
    modelType = 'bodyPix',
    segmentationThreshold = 0.7,
    enablePostProcessing = true
  } = options;

  try {
    // Dynamic import for better performance
    const [tf, bodyPix] = await Promise.all([
      import('@tensorflow/tfjs'),
      import('@tensorflow-models/body-pix')
    ]);

    // Set optimal backend
    await tf.setBackend('webgl');
    await tf.ready();

    // Load the model with optimized settings for better quality
    const net = await bodyPix.load({
      architecture: 'MobileNetV1',
      outputStride: 8, // Lower output stride for better accuracy
      multiplier: 1.0, // Higher multiplier for better quality
      quantBytes: 4, // Higher quantization for better quality
    });

    canvas.width = image.width;
    canvas.height = image.height;

    // Perform person segmentation with highest quality settings
    const segmentation = await net.segmentPerson(image, {
      flipHorizontal: false,
      internalResolution: 'full', // Use full resolution
      segmentationThreshold: segmentationThreshold,
      maxDetections: 10,
      scoreThreshold: 0.2,
      nmsRadius: 30,
    });

    // Draw the original image
    const ctx = canvas.getContext('2d');
    if (!ctx) return canvas;

    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Apply segmentation mask with gradient edges for smoother results
    for (let i = 0; i < segmentation.data.length; i++) {
      const isPerson = segmentation.data[i];
      if (!isPerson) {
        // Set alpha to 0 for background pixels
        data[i * 4 + 3] = 0;
      } else {
        // Ensure person pixels are fully opaque
        data[i * 4 + 3] = 255;
      }
    }

    // Put the processed image data back
    ctx.putImageData(imageData, 0, 0);

    // Apply enhanced post-processing for better edges
    if (enablePostProcessing) {
      applyAdvancedEdgeSmoothing(canvas);
    }

    // Clean up model to free memory
    net.dispose();

    return canvas;
  } catch (error) {
    console.error('AI model processing failed:', error);
    // Fallback to enhanced edge detection
    return processEdgeDetection(image, canvas, options);
  }
};
