"use client";

import React from "react";

// Enhanced Color Filter Background Removal
export const processColorFilter = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    threshold?: number;
    colorTolerance?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { threshold = 220, colorTolerance = 25 } = options;

    canvas.width = image.width;
    canvas.height = image.height;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Enhanced background color detection
    const edgeColors: number[][] = [];
    const cornerColors: number[][] = [];
    const sampleSize = 20; // Increased sample size

    // Sample corners first (most likely to be background)
    const corners = [
      [0, 0], [width - 1, 0], [0, height - 1], [width - 1, height - 1]
    ];

    corners.forEach(([x, y]) => {
      const idx = (y * width + x) * 4;
      if (idx < data.length) {
        cornerColors.push([data[idx], data[idx + 1], data[idx + 2]]);
      }
    });

    // Sample from all four edges with higher density
    for (let i = 0; i < sampleSize; i++) {
      const ratio = i / (sampleSize - 1);

      // Top and bottom edges
      const topX = Math.floor(ratio * (width - 1));
      const topIdx = topX * 4;
      if (topIdx < data.length) {
        edgeColors.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      }

      const bottomIdx = ((height - 1) * width + topX) * 4;
      if (bottomIdx < data.length) {
        edgeColors.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
      }

      // Left and right edges
      const leftY = Math.floor(ratio * (height - 1));
      const leftIdx = (leftY * width) * 4;
      if (leftIdx < data.length) {
        edgeColors.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      }

      const rightIdx = (leftY * width + width - 1) * 4;
      if (rightIdx < data.length) {
        edgeColors.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
      }
    }

    // Enhanced background color clustering
    const allColors = [...cornerColors, ...edgeColors];
    const colorClusters: { colors: number[][], weight: number }[] = [];

    // Group similar colors
    allColors.forEach(color => {
      let addedToCluster = false;

      for (const cluster of colorClusters) {
        const avgColor = cluster.colors.reduce((acc, c) => [
          acc[0] + c[0], acc[1] + c[1], acc[2] + c[2]
        ], [0, 0, 0]).map(sum => sum / cluster.colors.length);

        const distance = Math.sqrt(
          Math.pow(avgColor[0] - color[0], 2) +
          Math.pow(avgColor[1] - color[1], 2) +
          Math.pow(avgColor[2] - color[2], 2)
        );

        if (distance < colorTolerance) {
          cluster.colors.push(color);
          cluster.weight++;
          addedToCluster = true;
          break;
        }
      }

      if (!addedToCluster) {
        colorClusters.push({ colors: [color], weight: 1 });
      }
    });

    // Sort by weight and get dominant background colors
    colorClusters.sort((a, b) => b.weight - a.weight);
    const backgroundColors: number[][] = [];

    colorClusters.slice(0, 3).forEach(cluster => {
      const avgR = cluster.colors.reduce((sum, c) => sum + c[0], 0) / cluster.colors.length;
      const avgG = cluster.colors.reduce((sum, c) => sum + c[1], 0) / cluster.colors.length;
      const avgB = cluster.colors.reduce((sum, c) => sum + c[2], 0) / cluster.colors.length;
      backgroundColors.push([avgR, avgG, avgB]);
    });

    // Enhanced color filtering algorithm
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

      let shouldRemove = false;

      // Check against sampled background colors
      for (const [bgR, bgG, bgB] of backgroundColors) {
        const colorDistance = Math.sqrt(
          Math.pow(r - bgR, 2) + Math.pow(g - bgG, 2) + Math.pow(b - bgB, 2)
        );
        if (colorDistance < colorTolerance) {
          shouldRemove = true;
          break;
        }
      }

      // Enhanced background pattern detection
      if (!shouldRemove) {
        const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
        const maxChannel = Math.max(r, g, b);
        const minChannel = Math.min(r, g, b);
        const saturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;

        // Very bright backgrounds
        const isVeryBright = r > threshold && g > threshold && b > threshold;

        // Very uniform colors (low saturation, high brightness)
        const isUniformBright = colorVariance < 15 && luminance > 200 && saturation < 0.2;

        // Green screen detection (more precise)
        const isGreenScreen = g > r + 30 && g > b + 30 && g > 100 && saturation > 0.3;

        // Blue screen detection (more precise)
        const isBlueScreen = b > r + 30 && b > g + 30 && b > 100 && saturation > 0.3;

        // Gray/white backgrounds (improved detection)
        const isGrayBackground = colorVariance < 25 && luminance > 170 && saturation < 0.15;

        // Detect very dark backgrounds
        const isDarkBackground = luminance < 40 && saturation < 0.3;

        // Detect highly saturated uniform backgrounds (like solid color backdrops)
        const isSaturatedUniform = saturation > 0.8 && colorVariance < 30;

        // Exclude object-like colors (mid-range luminance with moderate saturation)
        const isLikelyObject = luminance > 60 && luminance < 200 && saturation > 0.2 && saturation < 0.9;

        shouldRemove = (isVeryBright || isUniformBright || isGreenScreen || isBlueScreen ||
                       isGrayBackground || isDarkBackground || isSaturatedUniform) && !isLikelyObject;
      }

      if (shouldRemove) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);

    setTimeout(() => resolve(canvas), 100);
  });
};

// Enhanced Edge Detection Background Removal
export const processEdgeDetection = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    edgeThreshold?: number;
    backgroundThreshold?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const { edgeThreshold = 25, backgroundThreshold = 0.3 } = options;

    canvas.width = image.width;
    canvas.height = image.height;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Create edge detection mask and gradient map
    const edgeMask = new Float32Array(width * height);
    const gradientMap = new Float32Array(width * height);

    // Enhanced Sobel edge detection with multiple kernels
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;

        // Get surrounding pixels (using luminance)
        const getPixelLuminance = (px: number, py: number) => {
          const pixelIdx = (py * width + px) * 4;
          const r = data[pixelIdx];
          const g = data[pixelIdx + 1];
          const b = data[pixelIdx + 2];
          return 0.299 * r + 0.587 * g + 0.114 * b;
        };

        const tl = getPixelLuminance(x - 1, y - 1);
        const tm = getPixelLuminance(x, y - 1);
        const tr = getPixelLuminance(x + 1, y - 1);
        const ml = getPixelLuminance(x - 1, y);
        const center = getPixelLuminance(x, y);
        const mr = getPixelLuminance(x + 1, y);
        const bl = getPixelLuminance(x - 1, y + 1);
        const bm = getPixelLuminance(x, y + 1);
        const br = getPixelLuminance(x + 1, y + 1);

        // Sobel X and Y gradients
        const sobelX = (tr + 2 * mr + br) - (tl + 2 * ml + bl);
        const sobelY = (bl + 2 * bm + br) - (tl + 2 * tm + tr);

        // Laplacian for additional edge detection
        const laplacian = Math.abs(8 * center - (tl + tm + tr + ml + mr + bl + bm + br));

        // Combined edge magnitude
        const edgeMagnitude = Math.sqrt(sobelX * sobelX + sobelY * sobelY) + laplacian * 0.3;

        edgeMask[idx] = edgeMagnitude > edgeThreshold ? 1 : 0;
        gradientMap[idx] = edgeMagnitude;
      }
    }

    // Enhanced morphological operations with multi-scale processing
    const cleanedEdgeMask = new Float32Array(width * height);
    const tempMask = new Float32Array(width * height);

    // First pass: Dilation to connect nearby edges
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        let maxVal = edgeMask[idx];

        // 3x3 dilation with weighted kernel
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = (y + dy) * width + (x + dx);
            const weight = (dx === 0 && dy === 0) ? 1.0 : 0.7;
            maxVal = Math.max(maxVal, edgeMask[neighborIdx] * weight);
          }
        }

        tempMask[idx] = maxVal;
      }
    }

    // Second pass: Erosion with gradient consideration
    for (let y = 2; y < height - 2; y++) {
      for (let x = 2; x < width - 2; x++) {
        const idx = y * width + x;
        let edgeStrength = 0;
        let totalGradient = 0;
        let count = 0;

        // 5x5 neighborhood analysis
        for (let dy = -2; dy <= 2; dy++) {
          for (let dx = -2; dx <= 2; dx++) {
            const neighborIdx = (y + dy) * width + (x + dx);
            const distance = Math.sqrt(dx * dx + dy * dy);
            const weight = Math.exp(-distance / 2); // Gaussian-like weighting

            edgeStrength += tempMask[neighborIdx] * weight;
            totalGradient += gradientMap[neighborIdx] * weight;
            count += weight;
          }
        }

        edgeStrength /= count;
        totalGradient /= count;

        // Adaptive threshold based on local characteristics
        const adaptiveThreshold = totalGradient > edgeThreshold * 1.5 ? 0.6 : 0.4;
        cleanedEdgeMask[idx] = edgeStrength > adaptiveThreshold ? 1 : 0;
      }
    }

    // Apply enhanced edge-based background removal
    for (let i = 0; i < data.length; i += 4) {
      const pixelIndex = Math.floor(i / 4);
      const x = pixelIndex % width;
      const y = Math.floor(pixelIndex / width);

      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;

      // Check if pixel is likely background
      const isEdge = cleanedEdgeMask[pixelIndex] > 0;
      const gradient = gradientMap[pixelIndex] || 0;

      // Color uniformity check
      const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
      const isUniform = colorVariance < 25;

      // Background likelihood factors
      const isBright = luminance > 200;
      const isVeryBright = luminance > 230;
      const isDark = luminance < 60;
      const isLowGradient = gradient < edgeThreshold * 0.5;

      // Distance from edges (simple approximation)
      let distanceFromEdge = 0;
      const searchRadius = 5;
      for (let dy = -searchRadius; dy <= searchRadius && distanceFromEdge === 0; dy++) {
        for (let dx = -searchRadius; dx <= searchRadius && distanceFromEdge === 0; dx++) {
          const checkY = y + dy;
          const checkX = x + dx;
          if (checkY >= 0 && checkY < height && checkX >= 0 && checkX < width) {
            const checkIdx = checkY * width + checkX;
            if (cleanedEdgeMask[checkIdx] > 0) {
              distanceFromEdge = Math.sqrt(dx * dx + dy * dy);
            }
          }
        }
      }

      // Decision logic for background removal
      let shouldRemove = false;

      if (isVeryBright && isUniform) {
        shouldRemove = true; // Very bright uniform areas
      } else if (!isEdge && isBright && isLowGradient) {
        shouldRemove = true; // Bright areas with low gradient and no edges
      } else if (isUniform && (isBright || isDark) && distanceFromEdge > 3) {
        shouldRemove = true; // Uniform areas far from edges
      } else if (luminance > 210 && colorVariance < 15 && isLowGradient) {
        shouldRemove = true; // Very uniform bright areas
      }

      if (shouldRemove) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }

    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);

    setTimeout(() => resolve(canvas), 500);
  });
};

// Advanced Color Analysis Background Removal with K-means Clustering
export const processAdvancedColorAnalysis = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    sampleSize?: number;
    threshold?: number;
    colorClusters?: number;
    backgroundRatio?: number;
  } = {}
): Promise<HTMLCanvasElement> => {
  return new Promise((resolve) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      resolve(canvas);
      return;
    }

    const {
      sampleSize = 30,
      threshold = 0.8,
      colorClusters = 8,
      backgroundRatio = 0.4
    } = options;

    canvas.width = image.width;
    canvas.height = image.height;

    // Draw original image
    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    const width = canvas.width;
    const height = canvas.height;

    // Enhanced edge and corner sampling
    const edgeColors: number[][] = [];
    const cornerColors: number[][] = [];

    // Sample corners (highest priority for background detection)
    const corners = [
      [0, 0], [width - 1, 0], [0, height - 1], [width - 1, height - 1]
    ];

    corners.forEach(([x, y]) => {
      const idx = (y * width + x) * 4;
      if (idx < data.length) {
        cornerColors.push([data[idx], data[idx + 1], data[idx + 2]]);
      }
    });

    // Enhanced edge sampling with higher density
    for (let i = 0; i < sampleSize; i++) {
      const ratio = i / (sampleSize - 1);

      // Top and bottom edges
      const topX = Math.floor(ratio * (width - 1));
      const topIdx = topX * 4;
      if (topIdx < data.length) {
        edgeColors.push([data[topIdx], data[topIdx + 1], data[topIdx + 2]]);
      }

      const bottomIdx = ((height - 1) * width + topX) * 4;
      if (bottomIdx < data.length) {
        edgeColors.push([data[bottomIdx], data[bottomIdx + 1], data[bottomIdx + 2]]);
      }

      // Left and right edges
      const leftY = Math.floor(ratio * (height - 1));
      const leftIdx = (leftY * width) * 4;
      if (leftIdx < data.length) {
        edgeColors.push([data[leftIdx], data[leftIdx + 1], data[leftIdx + 2]]);
      }

      const rightIdx = (leftY * width + width - 1) * 4;
      if (rightIdx < data.length) {
        edgeColors.push([data[rightIdx], data[rightIdx + 1], data[rightIdx + 2]]);
      }
    }

    // K-means clustering for background color detection
    const allColors = [...cornerColors, ...edgeColors];
    const clusters: {
      center: number[],
      pixels: number[],
      weight: number,
      isBackground: boolean
    }[] = [];

    // Initialize clusters
    for (let i = 0; i < Math.min(colorClusters, allColors.length); i++) {
      const randomColor = allColors[Math.floor(Math.random() * allColors.length)];
      clusters.push({
        center: [...randomColor],
        pixels: [],
        weight: 0,
        isBackground: false
      });
    }

    // K-means iterations
    for (let iter = 0; iter < 10; iter++) {
      // Clear previous assignments
      clusters.forEach(cluster => {
        cluster.pixels = [];
        cluster.weight = 0;
      });

      // Assign colors to nearest cluster
      allColors.forEach((color, index) => {
        let minDistance = Infinity;
        let nearestCluster = 0;

        clusters.forEach((cluster, clusterIndex) => {
          const distance = Math.sqrt(
            Math.pow(color[0] - cluster.center[0], 2) +
            Math.pow(color[1] - cluster.center[1], 2) +
            Math.pow(color[2] - cluster.center[2], 2)
          );

          if (distance < minDistance) {
            minDistance = distance;
            nearestCluster = clusterIndex;
          }
        });

        clusters[nearestCluster].pixels.push(index);
        // Give higher weight to corner colors
        const isCorner = index < cornerColors.length;
        clusters[nearestCluster].weight += isCorner ? 3 : 1;
      });

      // Update cluster centers
      clusters.forEach(cluster => {
        if (cluster.pixels.length > 0) {
          const avgR = cluster.pixels.reduce((sum, pixelIndex) => {
            const color = pixelIndex < cornerColors.length ?
              cornerColors[pixelIndex] :
              edgeColors[pixelIndex - cornerColors.length];
            return sum + color[0];
          }, 0) / cluster.pixels.length;

          const avgG = cluster.pixels.reduce((sum, pixelIndex) => {
            const color = pixelIndex < cornerColors.length ?
              cornerColors[pixelIndex] :
              edgeColors[pixelIndex - cornerColors.length];
            return sum + color[1];
          }, 0) / cluster.pixels.length;

          const avgB = cluster.pixels.reduce((sum, pixelIndex) => {
            const color = pixelIndex < cornerColors.length ?
              cornerColors[pixelIndex] :
              edgeColors[pixelIndex - cornerColors.length];
            return sum + color[2];
          }, 0) / cluster.pixels.length;

          cluster.center = [avgR, avgG, avgB];
        }
      });
    }

    // Identify background clusters based on weight and characteristics
    clusters.sort((a, b) => b.weight - a.weight);

    const backgroundClusters = clusters.slice(0, Math.ceil(colorClusters * backgroundRatio))
      .filter(cluster => {
        const [r, g, b] = cluster.center;
        const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
        const maxChannel = Math.max(r, g, b);
        const minChannel = Math.min(r, g, b);
        const saturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;

        // Background characteristics: high/low luminance, low saturation, or high weight
        const isBackgroundLike = luminance > 200 || luminance < 50 ||
                                saturation < 0.3 || cluster.weight > allColors.length * 0.2;

        cluster.isBackground = isBackgroundLike;
        return isBackgroundLike;
      });

    // Apply intelligent background removal
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
      const maxChannel = Math.max(r, g, b);
      const minChannel = Math.min(r, g, b);
      const saturation = maxChannel > 0 ? (maxChannel - minChannel) / maxChannel : 0;

      let shouldRemove = false;
      let minDistance = Infinity;

      // Check against background clusters
      for (const cluster of backgroundClusters) {
        const distance = Math.sqrt(
          Math.pow(r - cluster.center[0], 2) +
          Math.pow(g - cluster.center[1], 2) +
          Math.pow(b - cluster.center[2], 2)
        );

        minDistance = Math.min(minDistance, distance);

        if (distance < 40) { // Close color match
          shouldRemove = true;
          break;
        }
      }

      // Additional background detection rules
      if (!shouldRemove) {
        // Very uniform bright/dark colors
        const colorVariance = Math.abs(r - g) + Math.abs(g - b) + Math.abs(r - b);
        const isUniform = colorVariance < 20;
        const isExtreme = luminance > 220 || luminance < 30;

        // Low saturation backgrounds
        const isDesaturated = saturation < 0.15 && luminance > 150;

        // Exclude object-like colors (moderate luminance and saturation)
        const isLikelyObject = luminance > 60 && luminance < 180 &&
                              saturation > 0.25 && saturation < 0.8;

        shouldRemove = (isUniform && isExtreme) || isDesaturated;
        shouldRemove = shouldRemove && !isLikelyObject;
      }

      // Apply graduated transparency for smoother edges
      if (shouldRemove) {
        if (minDistance < 20) {
          data[i + 3] = 0; // Fully transparent
        } else if (minDistance < 60) {
          // Gradient transparency
          const alpha = Math.floor((minDistance - 20) * 6.375);
          data[i + 3] = Math.min(255, alpha);
        } else {
          data[i + 3] = 0;
        }
      }
    }

    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);

    setTimeout(() => resolve(canvas), 600);
  });
};

// Post-processing utilities
const applyMorphologicalOperations = (
  imageData: ImageData,
  operation: 'erode' | 'dilate' | 'smooth'
): ImageData => {
  const { data, width, height } = imageData;
  const newData = new Uint8ClampedArray(data);

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      const alpha = data[idx + 3];

      if (operation === 'smooth' && alpha > 0 && alpha < 255) {
        // Smooth alpha edges
        let alphaSum = 0;
        let count = 0;

        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            alphaSum += data[neighborIdx + 3];
            count++;
          }
        }

        newData[idx + 3] = Math.round(alphaSum / count);
      } else if (operation === 'erode' && alpha > 0) {
        // Erode edges (make transparent areas larger)
        let minAlpha = 255;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            minAlpha = Math.min(minAlpha, data[neighborIdx + 3]);
          }
        }
        newData[idx + 3] = minAlpha;
      } else if (operation === 'dilate' && alpha === 0) {
        // Dilate edges (make opaque areas larger)
        let maxAlpha = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            maxAlpha = Math.max(maxAlpha, data[neighborIdx + 3]);
          }
        }
        if (maxAlpha > 128) {
          newData[idx + 3] = maxAlpha;
        }
      }
    }
  }

  return new ImageData(newData, width, height);
};

const applyEdgeSmoothing = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

  // Apply smoothing operations
  let smoothedData = applyMorphologicalOperations(imageData, 'smooth');
  smoothedData = applyMorphologicalOperations(smoothedData, 'smooth');

  ctx.putImageData(smoothedData, 0, 0);
};

const applyAdvancedEdgeSmoothing = (canvas: HTMLCanvasElement): void => {
  const ctx = canvas.getContext('2d');
  if (!ctx) return;

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const { data, width, height } = imageData;
  const newData = new Uint8ClampedArray(data);

  // Apply Gaussian blur to alpha channel for smoother edges
  const kernel = [
    [1, 2, 1],
    [2, 4, 2],
    [1, 2, 1]
  ];
  const kernelSum = 16;

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      const idx = (y * width + x) * 4;
      const alpha = data[idx + 3];

      // Only smooth edge pixels (partial transparency)
      if (alpha > 0 && alpha < 255) {
        let alphaSum = 0;

        // Apply Gaussian kernel
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const neighborIdx = ((y + ky) * width + (x + kx)) * 4;
            const weight = kernel[ky + 1][kx + 1];
            alphaSum += data[neighborIdx + 3] * weight;
          }
        }

        newData[idx + 3] = Math.round(alphaSum / kernelSum);
      }
    }
  }

  // Apply feathering for very smooth edges
  for (let y = 2; y < height - 2; y++) {
    for (let x = 2; x < width - 2; x++) {
      const idx = (y * width + x) * 4;
      const alpha = newData[idx + 3];

      if (alpha > 0 && alpha < 255) {
        // Check surrounding pixels for additional smoothing
        let edgePixels = 0;
        let totalAlpha = 0;

        for (let dy = -2; dy <= 2; dy++) {
          for (let dx = -2; dx <= 2; dx++) {
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            const neighborAlpha = newData[neighborIdx + 3];
            if (neighborAlpha > 0) {
              edgePixels++;
              totalAlpha += neighborAlpha;
            }
          }
        }

        if (edgePixels > 0) {
          const avgAlpha = totalAlpha / edgePixels;
          newData[idx + 3] = Math.round((alpha + avgAlpha) / 2);
        }
      }
    }
  }

  // Copy smoothed alpha values back
  for (let i = 3; i < data.length; i += 4) {
    data[i] = newData[i];
  }

  ctx.putImageData(imageData, 0, 0);
};

// Enhanced MediaPipe Selfie Segmentation
export const processMediaPipeSelfie = async (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    segmentationThreshold?: number;
    enablePostProcessing?: boolean;
    modelSelection?: 0 | 1; // 0: general, 1: landscape
  } = {}
): Promise<HTMLCanvasElement> => {
  const {
    segmentationThreshold = 0.5,
    enablePostProcessing = true,
    modelSelection = 1
  } = options;

  try {
    // Dynamic import for MediaPipe
    const { SelfieSegmentation } = await import('@mediapipe/selfie_segmentation');

    return new Promise((resolve, reject) => {
      const selfieSegmentation = new SelfieSegmentation({
        locateFile: (file) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/selfie_segmentation/${file}`;
        }
      });

      selfieSegmentation.setOptions({
        modelSelection: modelSelection,
        selfieMode: false,
      });

      selfieSegmentation.onResults((results) => {
        canvas.width = image.width;
        canvas.height = image.height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        // Draw original image
        ctx.drawImage(image, 0, 0);

        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Apply segmentation mask with enhanced edge processing
        if (results.segmentationMask) {
          const maskCanvas = document.createElement('canvas');
          const maskCtx = maskCanvas.getContext('2d');
          if (maskCtx) {
            maskCanvas.width = canvas.width;
            maskCanvas.height = canvas.height;
            maskCtx.drawImage(results.segmentationMask, 0, 0, canvas.width, canvas.height);

            const maskData = maskCtx.getImageData(0, 0, canvas.width, canvas.height);

            // Enhanced mask processing with gradient edges
            for (let i = 0; i < data.length; i += 4) {
              const maskValue = maskData.data[i] / 255; // Normalize to 0-1

              if (maskValue < segmentationThreshold) {
                // Background - apply gradient transparency
                const alpha = Math.max(0, Math.min(255, maskValue * 512)); // Soft edge
                data[i + 3] = alpha;
              } else {
                // Foreground - keep with slight edge softening
                const alpha = Math.min(255, 128 + maskValue * 127);
                data[i + 3] = alpha;
              }
            }
          }
        }

        // Put processed image data back
        ctx.putImageData(imageData, 0, 0);

        // Apply post-processing for smoother edges
        if (enablePostProcessing) {
          applyAdvancedEdgeSmoothing(canvas);
        }

        selfieSegmentation.close();
        resolve(canvas);
      });

      // Send image to MediaPipe
      selfieSegmentation.send({ image: image });
    });
  } catch (error) {
    console.error('MediaPipe processing failed:', error);
    // Fallback to AI model
    return processAIModel(image, canvas, options);
  }
};

// Advanced WebAssembly-based Background Removal
export const processWasmAIModel = async (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    segmentationThreshold?: number;
    enablePostProcessing?: boolean;
    method?: 'person' | 'parts';
  } = {}
): Promise<HTMLCanvasElement> => {
  // Try MediaPipe first for better person segmentation
  try {
    return await processMediaPipeSelfie(image, canvas, options);
  } catch (error) {
    console.error('MediaPipe fallback failed:', error);
    // Fallback to the regular AI model
    return processAIModel(image, canvas, options);
  }
};

// Enhanced AI-based Background Removal using TensorFlow.js
export const processAIModel = async (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  options: {
    modelType?: 'bodyPix' | 'deeplab';
    segmentationThreshold?: number;
    enablePostProcessing?: boolean;
  } = {}
): Promise<HTMLCanvasElement> => {
  const {
    modelType = 'bodyPix',
    segmentationThreshold = 0.7,
    enablePostProcessing = true
  } = options;

  try {
    // Dynamic import for better performance
    const [tf, bodyPix] = await Promise.all([
      import('@tensorflow/tfjs'),
      import('@tensorflow-models/body-pix')
    ]);

    // Set optimal backend
    await tf.setBackend('webgl');
    await tf.ready();

    // Load the model with optimized settings for better quality
    const net = await bodyPix.load({
      architecture: 'ResNet50', // Better architecture for accuracy
      outputStride: 16, // Balanced for speed and accuracy
      multiplier: 1.0,
      quantBytes: 4,
    });

    canvas.width = image.width;
    canvas.height = image.height;

    // Multi-scale segmentation for better accuracy
    const segmentations = await Promise.all([
      // High quality segmentation
      net.segmentPerson(image, {
        flipHorizontal: false,
        internalResolution: 'full',
        segmentationThreshold: segmentationThreshold - 0.1,
        maxDetections: 5,
        scoreThreshold: 0.3,
        nmsRadius: 20,
      }),
      // Medium quality for edge refinement
      net.segmentPerson(image, {
        flipHorizontal: false,
        internalResolution: 'medium',
        segmentationThreshold: segmentationThreshold,
        maxDetections: 3,
        scoreThreshold: 0.4,
        nmsRadius: 15,
      })
    ]);

    // Combine segmentations for better results
    const combinedSegmentation = {
      data: new Uint8Array(segmentations[0].data.length),
      width: segmentations[0].width,
      height: segmentations[0].height
    };

    for (let i = 0; i < combinedSegmentation.data.length; i++) {
      // Use logical OR to combine segmentations (more inclusive)
      combinedSegmentation.data[i] = segmentations[0].data[i] || segmentations[1].data[i] ? 1 : 0;
    }

    // Draw the original image
    const ctx = canvas.getContext('2d');
    if (!ctx) return canvas;

    ctx.drawImage(image, 0, 0);

    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Apply enhanced segmentation mask with intelligent edge processing
    const width = canvas.width;
    const height = canvas.height;

    // Create edge map for better boundary detection
    const edgeMap = new Float32Array(width * height);
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        const center = combinedSegmentation.data[idx];

        // Check 8-connected neighbors
        let edgeStrength = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            if (dx === 0 && dy === 0) continue;
            const neighborIdx = (y + dy) * width + (x + dx);
            const neighbor = combinedSegmentation.data[neighborIdx];
            if (center !== neighbor) {
              edgeStrength += 1;
            }
          }
        }
        edgeMap[idx] = edgeStrength / 8.0;
      }
    }

    // Apply segmentation with gradient alpha for smooth edges
    for (let i = 0; i < combinedSegmentation.data.length; i++) {
      const isPerson = combinedSegmentation.data[i];
      const edgeStrength = edgeMap[i];

      if (!isPerson) {
        // Background pixels
        if (edgeStrength > 0.3) {
          // Near edge - apply gradient alpha
          const alpha = Math.max(0, 255 * (1 - edgeStrength * 2));
          data[i * 4 + 3] = alpha;
        } else {
          // Far from edge - fully transparent
          data[i * 4 + 3] = 0;
        }
      } else {
        // Person pixels
        if (edgeStrength > 0.3) {
          // Near edge - slightly reduce alpha for smoother transition
          const alpha = Math.min(255, 255 * (0.8 + edgeStrength * 0.2));
          data[i * 4 + 3] = alpha;
        } else {
          // Interior - fully opaque
          data[i * 4 + 3] = 255;
        }
      }
    }

    // Put the processed image data back
    ctx.putImageData(imageData, 0, 0);

    // Apply enhanced post-processing for better edges
    if (enablePostProcessing) {
      applyAdvancedEdgeSmoothing(canvas);
    }

    // Clean up model to free memory
    net.dispose();

    return canvas;
  } catch (error) {
    console.error('AI model processing failed:', error);
    // Fallback to enhanced edge detection
    return processEdgeDetection(image, canvas, options);
  }
};
