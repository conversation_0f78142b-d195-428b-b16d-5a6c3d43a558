"use client";

import React, { useCallback, useRef, useEffect, useState } from "react";
import * as tf from '@tensorflow/tfjs';
import * as bodyPix from '@tensorflow-models/body-pix';
import { toast } from "sonner";

interface AIBackgroundRemoverProps {
  image: HTMLImageElement;
  onResult: (canvas: HTMLCanvasElement) => void;
  onProgress: (progress: number) => void;
  onError: (error: string) => void;
}

export default function AIBackgroundRemover({
  image,
  onResult,
  onProgress,
  onError
}: AIBackgroundRemoverProps) {
  const [model, setModel] = useState<bodyPix.BodyPix | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Load BodyPix model
  const loadModel = useCallback(async () => {
    try {
      setIsLoading(true);
      onProgress(10);
      
      // Set TensorFlow.js backend
      await tf.ready();
      onProgress(20);
      
      // Load BodyPix model with optimized configuration
      const net = await bodyPix.load({
        architecture: 'MobileNetV1',
        outputStride: 16,
        multiplier: 0.75,
        quantBytes: 2
      });
      
      onProgress(50);
      setModel(net);
      setIsLoading(false);
      
      return net;
    } catch (error) {
      console.error('Failed to load BodyPix model:', error);
      onError('Failed to load AI model');
      setIsLoading(false);
      return null;
    }
  }, [onProgress, onError]);

  // Process image with BodyPix
  const processImage = useCallback(async (bodyPixModel: bodyPix.BodyPix) => {
    try {
      if (!canvasRef.current) return;
      
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Set canvas dimensions
      canvas.width = image.width;
      canvas.height = image.height;
      
      onProgress(60);
      
      // Perform person segmentation
      const segmentation = await bodyPixModel.segmentPerson(image, {
        flipHorizontal: false,
        internalResolution: 'medium',
        segmentationThreshold: 0.7,
        maxDetections: 10,
        scoreThreshold: 0.3,
        nmsRadius: 20,
      });
      
      onProgress(80);
      
      // Create mask from segmentation
      const mask = segmentation.data;
      
      // Draw original image
      ctx.drawImage(image, 0, 0);
      
      // Get image data
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      // Apply mask to remove background
      for (let i = 0; i < mask.length; i++) {
        const pixelIndex = i * 4;
        
        // If pixel is background (mask value is 0), make it transparent
        if (mask[i] === 0) {
          data[pixelIndex + 3] = 0; // Set alpha to 0 (transparent)
        }
      }
      
      // Put processed image data back
      ctx.putImageData(imageData, 0, 0);
      
      onProgress(100);
      onResult(canvas);
      
    } catch (error) {
      console.error('Failed to process image:', error);
      onError('Failed to process image with AI');
    }
  }, [image, onProgress, onResult, onError]);

  // Start processing
  const startProcessing = useCallback(async () => {
    try {
      let bodyPixModel = model;
      
      // Load model if not already loaded
      if (!bodyPixModel) {
        bodyPixModel = await loadModel();
        if (!bodyPixModel) return;
      }
      
      // Process the image
      await processImage(bodyPixModel);
      
    } catch (error) {
      console.error('Processing failed:', error);
      onError('Processing failed');
    }
  }, [model, loadModel, processImage, onError]);

  // Auto-start processing when component mounts
  useEffect(() => {
    if (image) {
      startProcessing();
    }
  }, [image, startProcessing]);

  return (
    <canvas
      ref={canvasRef}
      style={{ display: 'none' }}
      width={image.width}
      height={image.height}
    />
  );
}
