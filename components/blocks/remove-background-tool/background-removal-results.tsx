"use client";

import React, { useCallback } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface ProcessingResult {
  id: string;
  name: string;
  canvas: HTMLCanvasElement;
  processing: boolean;
  progress: number;
  error?: string;
}

interface BackgroundRemovalResultsProps {
  results: ProcessingResult[];
  isProcessing: boolean;
}

export default function BackgroundRemovalResults({ 
  results, 
  isProcessing 
}: BackgroundRemovalResultsProps) {
  const t = useTranslations();

  const downloadImage = useCallback((canvas: HTMLCanvasElement, methodName: string) => {
    try {
      // Create download link
      const link = document.createElement('a');
      link.download = `background-removed-${methodName.toLowerCase().replace(/\s+/g, '-')}.png`;
      link.href = canvas.toDataURL('image/png');
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success(t('remove_background.download_success'));
    } catch (error) {
      console.error('Download error:', error);
      toast.error(t('remove_background.download_error'));
    }
  }, [t]);

  const getMethodIcon = (methodId: string) => {
    switch (methodId) {
      case 'color-filter':
        return 'HiOutlineColorSwatch';
      case 'edge-detection':
        return 'HiOutlineSparkles';
      case 'advanced-analysis':
        return 'HiOutlineBeaker';
      case 'ai-model':
        return 'HiOutlineCpuChip';
      case 'mediapipe-model':
        return 'HiOutlineSparkles';
      case 'wasm-ai-model':
        return 'HiOutlineCog';
      default:
        return 'HiOutlinePhoto';
    }
  };

  const getMethodDescription = (methodId: string) => {
    switch (methodId) {
      case 'color-filter':
        return t('remove_background.method_color_filter_desc');
      case 'edge-detection':
        return t('remove_background.method_edge_detection_desc');
      case 'advanced-analysis':
        return t('remove_background.method_advanced_analysis_desc');
      case 'ai-model':
        return t('remove_background.method_ai_model_desc');
      case 'mediapipe-model':
        return 'Advanced AI for complex backgrounds and people';
      case 'wasm-ai-model':
        return 'High-performance WebAssembly AI processing';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">
          {t("remove_background.results_title")}
        </h3>
        <p className="text-muted-foreground">
          {t("remove_background.results_description")}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {results.map((result) => (
          <Card key={result.id} className="overflow-hidden">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center gap-2">
                  <Icon 
                    name={getMethodIcon(result.id)} 
                    className="w-5 h-5 text-primary" 
                  />
                  <span>{result.name}</span>
                </div>
                {result.processing && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="animate-pulse">
                      {t('remove_background.processing')}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {result.progress}%
                    </span>
                  </div>
                )}
                {!result.processing && !result.error && (
                  <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    {t('remove_background.completed')}
                  </Badge>
                )}
                {result.error && (
                  <Badge variant="destructive">
                    {t('remove_background.error')}
                  </Badge>
                )}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                {getMethodDescription(result.id)}
              </p>
            </CardHeader>
            <CardContent className="p-4">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4 min-h-[200px] flex items-center justify-center">
                {result.processing ? (
                  <div className="flex flex-col items-center gap-3">
                    <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm text-muted-foreground">
                      {t('remove_background.processing_method')}
                    </span>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${result.progress}%` }}
                      ></div>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {result.progress}%
                    </span>
                  </div>
                ) : result.error ? (
                  <div className="flex flex-col items-center gap-3 text-red-500">
                    <Icon name="HiOutlineExclamationTriangle" className="w-8 h-8" />
                    <span className="text-sm">{result.error}</span>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <canvas
                      ref={(canvas) => {
                        if (canvas && result.canvas) {
                          const ctx = canvas.getContext('2d');
                          if (ctx) {
                            canvas.width = result.canvas.width;
                            canvas.height = result.canvas.height;
                            ctx.drawImage(result.canvas, 0, 0);
                          }
                        }
                      }}
                      className="max-w-full max-h-[200px] object-contain border border-gray-200 dark:border-gray-700 rounded"
                      style={{
                        background: 'repeating-conic-gradient(#808080 0% 25%, transparent 0% 50%) 50% / 20px 20px'
                      }}
                    />
                  </div>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => downloadImage(result.canvas, result.name)}
                  disabled={result.processing || !!result.error}
                  className="flex-1"
                  size="sm"
                >
                  <Icon name="HiOutlineArrowDownTray" className="w-4 h-4 mr-2" />
                  {t('remove_background.download')}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {isProcessing && (
        <div className="text-center py-6">
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-blue-700 dark:text-blue-300 font-medium">
              {t('remove_background.processing_all_methods')}
            </span>
          </div>
        </div>
      )}

      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Icon name="HiOutlineInformationCircle" className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="font-medium mb-1">{t('remove_background.tip_title')}</p>
            <p>{t('remove_background.tip_description')}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
