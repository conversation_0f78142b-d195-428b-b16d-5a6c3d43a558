"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";

interface PerformanceIndicatorProps {
  isWasmSupported: boolean;
  className?: string;
}

export default function PerformanceIndicator({ 
  isWasmSupported, 
  className 
}: PerformanceIndicatorProps) {
  return (
    <Card className={cn("border-l-4", className, {
      "border-l-green-500 bg-green-50 dark:bg-green-950": isWasmSupported,
      "border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950": !isWasmSupported
    })}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className={cn("p-2 rounded-full", {
            "bg-green-100 dark:bg-green-900": isWasmSupported,
            "bg-yellow-100 dark:bg-yellow-900": !isWasmSupported
          })}>
            <Icon 
              name={isWasmSupported ? "HiOutlineSparkles" : "HiOutlineInformationCircle"} 
              className={cn("w-5 h-5", {
                "text-green-600 dark:text-green-400": isWasmSupported,
                "text-yellow-600 dark:text-yellow-400": !isWasmSupported
              })}
            />
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className={cn("font-semibold", {
                "text-green-800 dark:text-green-200": isWasmSupported,
                "text-yellow-800 dark:text-yellow-200": !isWasmSupported
              })}>
                {isWasmSupported ? "Enhanced Performance Available" : "Standard Performance Mode"}
              </h3>
              
              <Badge variant={isWasmSupported ? "default" : "secondary"}>
                {isWasmSupported ? "WebAssembly" : "JavaScript"}
              </Badge>
            </div>
            
            <p className={cn("text-sm", {
              "text-green-700 dark:text-green-300": isWasmSupported,
              "text-yellow-700 dark:text-yellow-300": !isWasmSupported
            })}>
              {isWasmSupported ? (
                <>
                  Your browser supports WebAssembly! You'll get access to our premium AI model 
                  with enhanced accuracy and faster processing for the best background removal results.
                </>
              ) : (
                <>
                  Your browser doesn't support all WebAssembly features. You'll still get great 
                  results with our standard AI model, but consider updating your browser for 
                  enhanced performance.
                </>
              )}
            </p>
            
            {isWasmSupported && (
              <div className="mt-3 flex flex-wrap gap-2">
                <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                  <Icon name="HiOutlineCheckCircle" className="w-4 h-4" />
                  <span>Higher Accuracy</span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                  <Icon name="HiOutlineLightningBolt" className="w-4 h-4" />
                  <span>Faster Processing</span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                  <Icon name="HiOutlineSparkles" className="w-4 h-4" />
                  <span>Smoother Edges</span>
                </div>
              </div>
            )}
            
            {!isWasmSupported && (
              <div className="mt-3">
                <p className="text-xs text-yellow-600 dark:text-yellow-400">
                  <strong>Tip:</strong> For the best experience, use a modern browser like 
                  Chrome 91+, Firefox 89+, Safari 15+, or Edge 91+.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Performance metrics component
export function PerformanceMetrics({ 
  processingTime, 
  imageSize, 
  method 
}: { 
  processingTime?: number; 
  imageSize?: { width: number; height: number }; 
  method?: string;
}) {
  if (!processingTime && !imageSize) return null;
  
  return (
    <div className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
      {processingTime && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlineClock" className="w-3 h-3" />
          <span>{processingTime.toFixed(1)}s</span>
        </div>
      )}
      
      {imageSize && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlinePhotograph" className="w-3 h-3" />
          <span>{imageSize.width} × {imageSize.height}</span>
        </div>
      )}
      
      {method && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlineCog" className="w-3 h-3" />
          <span>{method}</span>
        </div>
      )}
    </div>
  );
}
