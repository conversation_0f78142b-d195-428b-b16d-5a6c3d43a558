"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";

interface PerformanceIndicatorProps {
  isWasmSupported: boolean;
  isMediaPipeSupported: boolean;
  className?: string;
}

export default function PerformanceIndicator({
  isWasmSupported,
  isMediaPipeSupported,
  className
}: PerformanceIndicatorProps) {
  // Determine the best available technology
  const bestTech = isMediaPipeSupported ? 'mediapipe' : isWasmSupported ? 'wasm' : 'standard';
  const isEnhanced = bestTech !== 'standard';

  return (
    <Card className={cn("border-l-4", className, {
      "border-l-blue-500 bg-blue-50 dark:bg-blue-950": bestTech === 'mediapipe',
      "border-l-green-500 bg-green-50 dark:bg-green-950": bestTech === 'wasm',
      "border-l-yellow-500 bg-yellow-50 dark:bg-yellow-950": bestTech === 'standard'
    })}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className={cn("p-2 rounded-full", {
            "bg-blue-100 dark:bg-blue-900": bestTech === 'mediapipe',
            "bg-green-100 dark:bg-green-900": bestTech === 'wasm',
            "bg-yellow-100 dark:bg-yellow-900": bestTech === 'standard'
          })}>
            <Icon
              name={isEnhanced ? "HiOutlineSparkles" : "HiOutlineInformationCircle"}
              className={cn("w-5 h-5", {
                "text-blue-600 dark:text-blue-400": bestTech === 'mediapipe',
                "text-green-600 dark:text-green-400": bestTech === 'wasm',
                "text-yellow-600 dark:text-yellow-400": bestTech === 'standard'
              })}
            />
          </div>

          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <h3 className={cn("font-semibold", {
                "text-blue-800 dark:text-blue-200": bestTech === 'mediapipe',
                "text-green-800 dark:text-green-200": bestTech === 'wasm',
                "text-yellow-800 dark:text-yellow-200": bestTech === 'standard'
              })}>
                {bestTech === 'mediapipe' && "Pro Performance - MediaPipe"}
                {bestTech === 'wasm' && "Enhanced Performance - WebAssembly"}
                {bestTech === 'standard' && "Standard Performance Mode"}
              </h3>

              <Badge variant={isEnhanced ? "default" : "secondary"}>
                {bestTech === 'mediapipe' && "MediaPipe"}
                {bestTech === 'wasm' && "WebAssembly"}
                {bestTech === 'standard' && "JavaScript"}
              </Badge>
            </div>
            
            <p className={cn("text-sm", {
              "text-blue-700 dark:text-blue-300": bestTech === 'mediapipe',
              "text-green-700 dark:text-green-300": bestTech === 'wasm',
              "text-yellow-700 dark:text-yellow-300": bestTech === 'standard'
            })}>
              {bestTech === 'mediapipe' && (
                <>
                  Excellent! Your browser supports MediaPipe - Google's state-of-the-art selfie segmentation model.
                  You'll get the highest quality background removal with superior edge detection and person recognition.
                </>
              )}
              {bestTech === 'wasm' && (
                <>
                  Great! Your browser supports WebAssembly. You'll get access to our premium AI model
                  with enhanced accuracy and faster processing for excellent background removal results.
                </>
              )}
              {bestTech === 'standard' && (
                <>
                  Your browser will use our standard AI model. You'll still get good results,
                  but consider updating your browser for enhanced performance with MediaPipe or WebAssembly support.
                </>
              )}
            </p>
            
            {isEnhanced && (
              <div className="mt-3 flex flex-wrap gap-2">
                {bestTech === 'mediapipe' && (
                  <>
                    <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
                      <Icon name="HiOutlineCheckCircle" className="w-4 h-4" />
                      <span>Highest Accuracy</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
                      <Icon name="HiOutlineUser" className="w-4 h-4" />
                      <span>Person Optimized</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-blue-600 dark:text-blue-400">
                      <Icon name="HiOutlineSparkles" className="w-4 h-4" />
                      <span>Superior Edges</span>
                    </div>
                  </>
                )}
                {bestTech === 'wasm' && (
                  <>
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <Icon name="HiOutlineCheckCircle" className="w-4 h-4" />
                      <span>Higher Accuracy</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <Icon name="HiOutlineLightningBolt" className="w-4 h-4" />
                      <span>Faster Processing</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <Icon name="HiOutlineSparkles" className="w-4 h-4" />
                      <span>Smoother Edges</span>
                    </div>
                  </>
                )}
              </div>
            )}
            
            {!isWasmSupported && (
              <div className="mt-3">
                <p className="text-xs text-yellow-600 dark:text-yellow-400">
                  <strong>Tip:</strong> For the best experience, use a modern browser like 
                  Chrome 91+, Firefox 89+, Safari 15+, or Edge 91+.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Performance metrics component
export function PerformanceMetrics({ 
  processingTime, 
  imageSize, 
  method 
}: { 
  processingTime?: number; 
  imageSize?: { width: number; height: number }; 
  method?: string;
}) {
  if (!processingTime && !imageSize) return null;
  
  return (
    <div className="flex flex-wrap gap-4 text-xs text-gray-500 dark:text-gray-400">
      {processingTime && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlineClock" className="w-3 h-3" />
          <span>{processingTime.toFixed(1)}s</span>
        </div>
      )}
      
      {imageSize && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlinePhotograph" className="w-3 h-3" />
          <span>{imageSize.width} × {imageSize.height}</span>
        </div>
      )}
      
      {method && (
        <div className="flex items-center space-x-1">
          <Icon name="HiOutlineCog" className="w-3 h-3" />
          <span>{method}</span>
        </div>
      )}
    </div>
  );
}
