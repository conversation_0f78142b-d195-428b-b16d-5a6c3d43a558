"use client";

import React, { useState, useRef, useCallback } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import ImageUpload from "@/components/blocks/image-upload";
import BackgroundRemovalResults from "./background-removal-results";

interface ProcessingResult {
  id: string;
  name: string;
  canvas: HTMLCanvasElement;
  processing: boolean;
  error?: string;
}

export default function RemoveBackgroundTool() {
  const t = useTranslations();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null);
  const [results, setResults] = useState<ProcessingResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleImageUpload = useCallback(async (file: File) => {
    setImageFile(file);
    setResults([]);
    
    // Load the image
    const img = new Image();
    img.onload = () => {
      setOriginalImage(img);
      startBackgroundRemoval(img);
    };
    img.src = URL.createObjectURL(file);
  }, []);

  const startBackgroundRemoval = useCallback(async (img: HTMLImageElement) => {
    setIsProcessing(true);
    
    // Initialize results with processing state
    const initialResults: ProcessingResult[] = [
      {
        id: 'color-filter',
        name: t('remove_background.method_color_filter'),
        canvas: document.createElement('canvas'),
        processing: true
      },
      {
        id: 'edge-detection',
        name: t('remove_background.method_edge_detection'),
        canvas: document.createElement('canvas'),
        processing: true
      },
      {
        id: 'ai-model',
        name: t('remove_background.method_ai_model'),
        canvas: document.createElement('canvas'),
        processing: true
      }
    ];
    
    setResults(initialResults);

    // Process each method
    try {
      // Method 1: Color Filter (fastest)
      setTimeout(() => {
        processColorFilter(img, initialResults[0]);
      }, 100);

      // Method 2: Edge Detection (medium speed)
      setTimeout(() => {
        processEdgeDetection(img, initialResults[1]);
      }, 500);

      // Method 3: AI Model (slowest, simulated)
      setTimeout(() => {
        processAIModel(img, initialResults[2]);
      }, 1500);

    } catch (error) {
      console.error('Background removal error:', error);
      toast.error(t('remove_background.processing_error'));
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
      }, 2000);
    }
  }, [t]);

  const processColorFilter = useCallback((img: HTMLImageElement, result: ProcessingResult) => {
    const canvas = result.canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = img.width;
    canvas.height = img.height;
    
    // Draw original image
    ctx.drawImage(img, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Simple white background removal
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // If pixel is close to white, make it transparent
      if (r > 240 && g > 240 && b > 240) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    // Update result
    setResults(prev => prev.map(r => 
      r.id === result.id ? { ...r, processing: false } : r
    ));
  }, []);

  const processEdgeDetection = useCallback((img: HTMLImageElement, result: ProcessingResult) => {
    const canvas = result.canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = img.width;
    canvas.height = img.height;
    
    // Draw original image
    ctx.drawImage(img, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Simple edge-based background removal
    // This is a simplified version - in reality, you'd use more sophisticated algorithms
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // Calculate luminance
      const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
      
      // If pixel is very bright or very uniform, likely background
      if (luminance > 230 || (Math.abs(r - g) < 10 && Math.abs(g - b) < 10)) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    // Update result
    setResults(prev => prev.map(r => 
      r.id === result.id ? { ...r, processing: false } : r
    ));
  }, []);

  const processAIModel = useCallback((img: HTMLImageElement, result: ProcessingResult) => {
    // Simulated AI processing - in a real implementation, you'd use TensorFlow.js + BodyPix
    const canvas = result.canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = img.width;
    canvas.height = img.height;
    
    // Draw original image
    ctx.drawImage(img, 0, 0);
    
    // Get image data
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;
    
    // Simulated AI-based background removal (more sophisticated)
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      
      // More complex logic simulating AI detection
      const isBackground = (
        (r > 200 && g > 200 && b > 200) || // Bright areas
        (r < 50 && g < 50 && b < 50) || // Very dark areas
        (Math.abs(r - g) < 20 && Math.abs(g - b) < 20 && r > 180) // Uniform bright colors
      );
      
      if (isBackground) {
        data[i + 3] = 0; // Set alpha to 0 (transparent)
      }
    }
    
    // Put processed image data back
    ctx.putImageData(imageData, 0, 0);
    
    // Update result
    setResults(prev => prev.map(r => 
      r.id === result.id ? { ...r, processing: false } : r
    ));
  }, []);

  const handleNewImage = useCallback(() => {
    setImageFile(null);
    setOriginalImage(null);
    setResults([]);
    setIsProcessing(false);
  }, []);

  if (!imageFile) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">{t("remove_background.tool_title")}</h2>
          <p className="text-muted-foreground text-lg">
            {t("remove_background.tool_description")}
          </p>
        </div>
        
        <ImageUpload
          onImageSelect={handleImageUpload}
          maxSize={10}
          acceptedFormats={["image/jpeg", "image/png", "image/webp", "image/gif"]}
          className="min-h-[300px]"
        />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">{t("remove_background.processing_title")}</h2>
        <p className="text-muted-foreground">
          {t("remove_background.processing_description")}
        </p>
      </div>

      <div className="space-y-8">
        {/* Original Image */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t("remove_background.original_image")}</span>
              <Button
                onClick={handleNewImage}
                variant="outline"
                size="sm"
              >
                <Icon name="HiOutlinePhoto" className="w-4 h-4 mr-2" />
                {t("remove_background.new_image")}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 flex justify-center">
              {originalImage && (
                <img
                  src={originalImage.src}
                  alt="Original"
                  className="max-w-full max-h-[400px] object-contain"
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Processing Results */}
        {results.length > 0 && (
          <BackgroundRemovalResults
            results={results}
            isProcessing={isProcessing}
          />
        )}
      </div>
    </div>
  );
}
