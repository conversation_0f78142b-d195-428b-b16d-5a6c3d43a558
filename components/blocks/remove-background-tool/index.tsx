"use client";

import React, { useState, useRef, useCallback, useEffect } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import ImageUpload from "@/components/blocks/image-upload";
import BackgroundRemovalResults from "./background-removal-results";
import AIBackgroundRemover from "./ai-background-remover";
import {
  processColorFilter,
  processEdgeDetection,
  processAdvancedColorAnalysis,
  processAIModel,
  processWasmAIModel
} from "./background-processors";
import WasmBackgroundRemover, { useWasmBackgroundRemover } from "./wasm-background-remover";
import MediaPipeBackgroundRemover, { useMediaPipeSupport } from "./mediapipe-background-remover";
import PerformanceIndicator from "./performance-indicator";

interface ProcessingResult {
  id: string;
  name: string;
  canvas: HTMLCanvasElement;
  processing: boolean;
  progress: number;
  error?: string;
}

export default function RemoveBackgroundTool() {
  const t = useTranslations();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [originalImage, setOriginalImage] = useState<HTMLImageElement | null>(null);
  const [results, setResults] = useState<ProcessingResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // WebAssembly and MediaPipe support detection
  const { isSupported: isWasmSupported } = useWasmBackgroundRemover();
  const { isSupported: isMediaPipeSupported } = useMediaPipeSupport();



  // Advanced processing options
  const [processingOptions, setProcessingOptions] = useState({
    colorFilter: {
      threshold: 220,
      colorTolerance: 25,
    },
    edgeDetection: {
      edgeThreshold: 25,
      backgroundThreshold: 0.3,
    },
    aiModel: {
      segmentationThreshold: 0.7,
      enablePostProcessing: true,
    },
    advancedColorAnalysis: {
      sampleSize: 20,
      threshold: 0.8,
    },
  });

  const handleImageUpload = useCallback(async (file: File) => {
    setImageFile(file);
    setResults([]);

    // Load the image
    const img = new Image();
    img.onload = () => {
      setOriginalImage(img);
    };
    img.src = URL.createObjectURL(file);
  }, []);

  const startBackgroundRemoval = useCallback(async (img: HTMLImageElement) => {
    setIsProcessing(true);

    // Initialize results with processing state
    const initialResults: ProcessingResult[] = [
      {
        id: 'color-filter',
        name: t('remove_background.method_color_filter'),
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      },
      {
        id: 'edge-detection',
        name: t('remove_background.method_edge_detection'),
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      },
      {
        id: 'advanced-analysis',
        name: t('remove_background.method_advanced_analysis'),
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      },
      // Add MediaPipe method if supported (highest priority)
      ...(isMediaPipeSupported ? [{
        id: 'mediapipe-model',
        name: 'MediaPipe Selfie Segmentation (Pro)',
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      }] : []),
      // Add WebAssembly AI method if supported
      ...(isWasmSupported ? [{
        id: 'wasm-ai-model',
        name: 'WebAssembly AI Model (Premium)',
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      }] : []),
      {
        id: 'ai-model',
        name: t('remove_background.method_ai_model'),
        canvas: document.createElement('canvas'),
        processing: true,
        progress: 0
      }
    ];

    setResults(initialResults);

    // Process each method
    try {
      // Method 1: Color Filter (fastest)
      processColorFilter(img, initialResults[0].canvas, processingOptions.colorFilter)
        .then(() => {
          setResults(prev => prev.map(r =>
            r.id === 'color-filter' ? { ...r, processing: false, progress: 100 } : r
          ));
        })
        .catch((error) => {
          setResults(prev => prev.map(r =>
            r.id === 'color-filter' ? { ...r, processing: false, error: 'Processing failed' } : r
          ));
        });

      // Method 2: Edge Detection (medium speed)
      processEdgeDetection(img, initialResults[1].canvas, processingOptions.edgeDetection)
        .then(() => {
          setResults(prev => prev.map(r =>
            r.id === 'edge-detection' ? { ...r, processing: false, progress: 100 } : r
          ));
        })
        .catch((error) => {
          setResults(prev => prev.map(r =>
            r.id === 'edge-detection' ? { ...r, processing: false, error: 'Processing failed' } : r
          ));
        });

      // Method 3: Advanced Color Analysis
      processAdvancedColorAnalysis(img, initialResults[2].canvas, processingOptions.advancedColorAnalysis)
        .then(() => {
          setResults(prev => prev.map(r =>
            r.id === 'advanced-analysis' ? { ...r, processing: false, progress: 100 } : r
          ));
        })
        .catch((error) => {
          setResults(prev => prev.map(r =>
            r.id === 'advanced-analysis' ? { ...r, processing: false, error: 'Processing failed' } : r
          ));
        });

    } catch (error) {
      console.error('Background removal error:', error);
      toast.error(t('remove_background.processing_error'));
    }
  }, [t, isMediaPipeSupported, isWasmSupported, processingOptions]);

  // Start background removal when image is loaded
  useEffect(() => {
    if (originalImage) {
      startBackgroundRemoval(originalImage);
    }
  }, [originalImage, startBackgroundRemoval]);

  // Handle AI processing result
  const handleAIResult = useCallback((canvas: HTMLCanvasElement) => {
    setResults(prev => prev.map(r =>
      r.id === 'ai-model' ? { ...r, processing: false, progress: 100, canvas } : r
    ));
    setIsProcessing(false);
  }, []);

  // Handle AI processing progress
  const handleAIProgress = useCallback((progress: number) => {
    setResults(prev => prev.map(r =>
      r.id === 'ai-model' ? { ...r, progress } : r
    ));
  }, []);

  // Handle AI processing error
  const handleAIError = useCallback((error: string) => {
    setResults(prev => prev.map(r =>
      r.id === 'ai-model' ? { ...r, processing: false, error } : r
    ));
    setIsProcessing(false);
  }, []);

  // Handle WebAssembly AI processing result
  const handleWasmAIResult = useCallback((canvas: HTMLCanvasElement) => {
    setResults(prev => prev.map(r =>
      r.id === 'wasm-ai-model' ? { ...r, processing: false, progress: 100, canvas } : r
    ));
    setIsProcessing(false);
  }, []);

  // Handle WebAssembly AI processing progress
  const handleWasmAIProgress = useCallback((progress: number) => {
    setResults(prev => prev.map(r =>
      r.id === 'wasm-ai-model' ? { ...r, progress } : r
    ));
  }, []);

  // Handle WebAssembly AI processing error
  const handleWasmAIError = useCallback((error: string) => {
    setResults(prev => prev.map(r =>
      r.id === 'wasm-ai-model' ? { ...r, processing: false, error } : r
    ));
    setIsProcessing(false);
  }, []);

  // Handle MediaPipe processing result
  const handleMediaPipeResult = useCallback((canvas: HTMLCanvasElement) => {
    setResults(prev => prev.map(r =>
      r.id === 'mediapipe-model' ? { ...r, processing: false, progress: 100, canvas } : r
    ));
    setIsProcessing(false);
  }, []);

  // Handle MediaPipe processing progress
  const handleMediaPipeProgress = useCallback((progress: number) => {
    setResults(prev => prev.map(r =>
      r.id === 'mediapipe-model' ? { ...r, progress } : r
    ));
  }, []);

  // Handle MediaPipe processing error
  const handleMediaPipeError = useCallback((error: string) => {
    setResults(prev => prev.map(r =>
      r.id === 'mediapipe-model' ? { ...r, processing: false, error } : r
    ));
    setIsProcessing(false);
  }, []);

  const handleNewImage = useCallback(() => {
    setImageFile(null);
    setOriginalImage(null);
    setResults([]);
    setIsProcessing(false);
  }, []);

  if (!imageFile) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">{t("remove_background.tool_title")}</h2>
          <p className="text-muted-foreground text-lg">
            {t("remove_background.tool_description")}
          </p>
        </div>
        
        <ImageUpload
          onImageSelect={handleImageUpload}
          maxSize={10}
          acceptedFormats={["image/jpeg", "image/png", "image/webp", "image/gif"]}
          className="min-h-[300px]"
        />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">{t("remove_background.processing_title")}</h2>
        <p className="text-muted-foreground">
          {t("remove_background.processing_description")}
        </p>
      </div>

      <div className="space-y-8">
        {/* Performance Indicator */}
        <PerformanceIndicator
          isWasmSupported={isWasmSupported}
          isMediaPipeSupported={isMediaPipeSupported}
        />

        {/* Advanced Options */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t("remove_background.advanced_options")}</span>
              <Button
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                variant="outline"
                size="sm"
              >
                {showAdvancedOptions ? t("common.hide") : t("common.show")}
              </Button>
            </CardTitle>
          </CardHeader>
          {showAdvancedOptions && (
            <CardContent className="space-y-6">
              {/* Color Filter Options */}
              <div className="space-y-4">
                <h4 className="font-medium">{t("remove_background.method_color_filter")}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("remove_background.color_threshold")}: {processingOptions.colorFilter.threshold}
                    </label>
                    <input
                      type="range"
                      min="180"
                      max="250"
                      value={processingOptions.colorFilter.threshold}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        colorFilter: { ...prev.colorFilter, threshold: parseInt(e.target.value) }
                      }))}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("remove_background.color_tolerance")}: {processingOptions.colorFilter.colorTolerance}
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="50"
                      value={processingOptions.colorFilter.colorTolerance}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        colorFilter: { ...prev.colorFilter, colorTolerance: parseInt(e.target.value) }
                      }))}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* Edge Detection Options */}
              <div className="space-y-4">
                <h4 className="font-medium">{t("remove_background.method_edge_detection")}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("remove_background.edge_threshold")}: {processingOptions.edgeDetection.edgeThreshold}
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="50"
                      value={processingOptions.edgeDetection.edgeThreshold}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        edgeDetection: { ...prev.edgeDetection, edgeThreshold: parseInt(e.target.value) }
                      }))}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("remove_background.background_threshold")}: {processingOptions.edgeDetection.backgroundThreshold}
                    </label>
                    <input
                      type="range"
                      min="0.1"
                      max="0.8"
                      step="0.1"
                      value={processingOptions.edgeDetection.backgroundThreshold}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        edgeDetection: { ...prev.edgeDetection, backgroundThreshold: parseFloat(e.target.value) }
                      }))}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* AI Model Options */}
              <div className="space-y-4">
                <h4 className="font-medium">{t("remove_background.method_ai_model")}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t("remove_background.segmentation_threshold")}: {processingOptions.aiModel.segmentationThreshold}
                    </label>
                    <input
                      type="range"
                      min="0.3"
                      max="0.9"
                      step="0.1"
                      value={processingOptions.aiModel.segmentationThreshold}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        aiModel: { ...prev.aiModel, segmentationThreshold: parseFloat(e.target.value) }
                      }))}
                      className="w-full"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="postProcessing"
                      checked={processingOptions.aiModel.enablePostProcessing}
                      onChange={(e) => setProcessingOptions(prev => ({
                        ...prev,
                        aiModel: { ...prev.aiModel, enablePostProcessing: e.target.checked }
                      }))}
                    />
                    <label htmlFor="postProcessing" className="text-sm font-medium">
                      {t("remove_background.enable_post_processing")}
                    </label>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => originalImage && startBackgroundRemoval(originalImage)}
                disabled={isProcessing}
                className="w-full"
              >
                {isProcessing ? t("remove_background.processing") : t("remove_background.reprocess")}
              </Button>
            </CardContent>
          )}
        </Card>

        {/* Original Image */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{t("remove_background.original_image")}</span>
              <Button
                onClick={handleNewImage}
                variant="outline"
                size="sm"
              >
                <Icon name="HiOutlinePhoto" className="w-4 h-4 mr-2" />
                {t("remove_background.new_image")}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 flex justify-center">
              {originalImage && (
                <img
                  src={originalImage.src}
                  alt="Original"
                  className="max-w-full max-h-[400px] object-contain"
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Processing Results */}
        {results.length > 0 && (
          <BackgroundRemovalResults
            results={results}
            isProcessing={isProcessing}
          />
        )}

        {/* MediaPipe Background Remover */}
        {originalImage && results.some(r => r.id === 'mediapipe-model' && r.processing) && (
          <MediaPipeBackgroundRemover
            image={originalImage}
            onResult={handleMediaPipeResult}
            onProgress={handleMediaPipeProgress}
            onError={handleMediaPipeError}
            options={{
              modelSelection: 1, // Use landscape model for better quality
              selfieMode: false,
              threshold: processingOptions.aiModel.segmentationThreshold,
              enablePostProcessing: processingOptions.postProcessing
            }}
          />
        )}

        {/* WebAssembly AI Background Remover */}
        {originalImage && results.some(r => r.id === 'wasm-ai-model' && r.processing) && (
          <WasmBackgroundRemover
            image={originalImage}
            onResult={handleWasmAIResult}
            onProgress={handleWasmAIProgress}
            onError={handleWasmAIError}
            options={{
              segmentationThreshold: processingOptions.aiModel.segmentationThreshold,
              enablePostProcessing: processingOptions.postProcessing,
              method: 'person'
            }}
          />
        )}

        {/* AI Background Remover */}
        {originalImage && results.some(r => r.id === 'ai-model' && r.processing) && (
          <AIBackgroundRemover
            image={originalImage}
            onResult={handleAIResult}
            onProgress={handleAIProgress}
            onError={handleAIError}
          />
        )}
      </div>
    </div>
  );
}
