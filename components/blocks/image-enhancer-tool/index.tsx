"use client";

import React, { useState, useRef, useCallback } from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import Icon from "@/components/icon";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import ImageUpload from "@/components/blocks/image-upload";
import ImagePreview from "./image-preview";
import EnhancementControls from "./enhancement-controls";

interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  denoise: number;
}

const defaultSettings: EnhancementSettings = {
  brightness: 100,
  contrast: 100,
  saturation: 100,
  sharpness: 0,
  denoise: 0,
};

export default function ImageEnhancerTool() {
  const t = useTranslations();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [settings, setSettings] = useState<EnhancementSettings>(defaultSettings);
  const [isProcessing, setIsProcessing] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const handleImageUpload = useCallback((file: File) => {
    setImageFile(file);
    setSettings(defaultSettings);
  }, []);

  const handleSettingChange = useCallback((key: keyof EnhancementSettings, value: number) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const handleReset = useCallback(() => {
    setSettings(defaultSettings);
  }, []);

  const handleDownload = useCallback(async () => {
    if (!canvasRef.current || !imageFile) {
      toast.error(t("image_enhancer.no_image_error"));
      return;
    }

    setIsProcessing(true);
    try {
      const canvas = canvasRef.current;
      const link = document.createElement("a");
      link.download = `enhanced_${imageFile.name}`;
      link.href = canvas.toDataURL("image/png");
      link.click();
      
      toast.success(t("image_enhancer.download_success"));
    } catch (error) {
      console.error("Download error:", error);
      toast.error(t("image_enhancer.download_error"));
    } finally {
      setIsProcessing(false);
    }
  }, [imageFile, t]);

  const handleNewImage = useCallback(() => {
    setImageFile(null);
    setSettings(defaultSettings);
  }, []);

  if (!imageFile) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">{t("image_enhancer.tool_title")}</h2>
          <p className="text-muted-foreground text-lg">
            {t("image_enhancer.tool_description")}
          </p>
        </div>
        
        <ImageUpload
          onImageSelect={handleImageUpload}
          accept="image/*"
          maxSize={10}
          className="min-h-[300px]"
        />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-4">{t("image_enhancer.enhance_title")}</h2>
        <p className="text-muted-foreground">
          {t("image_enhancer.enhance_description")}
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Image Preview */}
        <div className="lg:col-span-2">
          <ImagePreview
            imageFile={imageFile}
            settings={settings}
            canvasRef={canvasRef}
          />
        </div>

        {/* Enhancement Controls */}
        <div className="space-y-6">
          <EnhancementControls
            settings={settings}
            onSettingChange={handleSettingChange}
            onReset={handleReset}
          />

          {/* Action Buttons */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <Button
                onClick={handleDownload}
                disabled={isProcessing}
                className="w-full"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <Icon name="HiOutlineArrowPath" className="w-4 h-4 mr-2 animate-spin" />
                    {t("image_enhancer.processing")}
                  </>
                ) : (
                  <>
                    <Icon name="HiOutlineArrowDownTray" className="w-4 h-4 mr-2" />
                    {t("image_enhancer.download_button")}
                  </>
                )}
              </Button>

              <Button
                onClick={handleNewImage}
                variant="outline"
                className="w-full"
                size="lg"
              >
                <Icon name="HiOutlinePhoto" className="w-4 h-4 mr-2" />
                {t("image_enhancer.new_image")}
              </Button>
            </CardContent>
          </Card>

          {/* Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">
                <Icon name="HiOutlineLightBulb" className="w-4 h-4 mr-2 inline" />
                {t("image_enhancer.tips_title")}
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 pt-0">
              <ul className="text-xs text-muted-foreground space-y-2">
                <li>• {t("image_enhancer.tip_1")}</li>
                <li>• {t("image_enhancer.tip_2")}</li>
                <li>• {t("image_enhancer.tip_3")}</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
