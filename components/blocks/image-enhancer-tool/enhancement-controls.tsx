"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import Icon from "@/components/icon";

interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  denoise: number;
}

interface EnhancementControlsProps {
  settings: EnhancementSettings;
  onSettingChange: (key: keyof EnhancementSettings, value: number) => void;
  onReset: () => void;
}

interface ControlItem {
  key: keyof EnhancementSettings;
  labelKey: string;
  min: number;
  max: number;
  step: number;
  icon: string;
  unit?: string;
}

export default function EnhancementControls({ 
  settings, 
  onSettingChange, 
  onReset 
}: EnhancementControlsProps) {
  const t = useTranslations();

  const controls: ControlItem[] = [
    {
      key: "brightness",
      labelKey: "brightness",
      min: 50,
      max: 200,
      step: 1,
      icon: "HiOutlineSun",
      unit: "%"
    },
    {
      key: "contrast",
      labelKey: "contrast",
      min: 50,
      max: 200,
      step: 1,
      icon: "HiOutlineAdjustmentsHorizontal",
      unit: "%"
    },
    {
      key: "saturation",
      labelKey: "saturation",
      min: 0,
      max: 200,
      step: 1,
      icon: "HiOutlineColorSwatch",
      unit: "%"
    },
    {
      key: "sharpness",
      labelKey: "sharpness",
      min: 0,
      max: 100,
      step: 1,
      icon: "HiOutlineSparkles"
    },
    {
      key: "denoise",
      labelKey: "denoise",
      min: 0,
      max: 100,
      step: 1,
      icon: "HiOutlineShieldCheck"
    }
  ];

  const handleSliderChange = (key: keyof EnhancementSettings, values: number[]) => {
    onSettingChange(key, values[0]);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            <Icon name="HiOutlineCog6Tooth" className="w-5 h-5 mr-2 inline" />
            {t("image_enhancer.controls_title")}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
          >
            <Icon name="HiOutlineArrowPath" className="w-4 h-4 mr-1" />
            {t("image_enhancer.reset")}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {controls.map((control) => (
          <div key={control.key} className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium flex items-center">
                <Icon name={control.icon} className="w-4 h-4 mr-2" />
                {t(`image_enhancer.${control.labelKey}`)}
              </label>
              <span className="text-sm text-muted-foreground">
                {settings[control.key]}{control.unit}
              </span>
            </div>
            
            <Slider
              value={[settings[control.key]]}
              onValueChange={(values) => handleSliderChange(control.key, values)}
              min={control.min}
              max={control.max}
              step={control.step}
              className="w-full"
            />
            
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{control.min}{control.unit}</span>
              <span>{control.max}{control.unit}</span>
            </div>
          </div>
        ))}
        
        {/* Preset Buttons */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-3">{t("image_enhancer.presets")}</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onSettingChange("brightness", 110);
                onSettingChange("contrast", 120);
                onSettingChange("saturation", 110);
                onSettingChange("sharpness", 20);
                onSettingChange("denoise", 10);
              }}
            >
              {t("image_enhancer.preset_enhance")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onSettingChange("brightness", 105);
                onSettingChange("contrast", 110);
                onSettingChange("saturation", 105);
                onSettingChange("sharpness", 10);
                onSettingChange("denoise", 20);
              }}
            >
              {t("image_enhancer.preset_soft")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onSettingChange("brightness", 115);
                onSettingChange("contrast", 140);
                onSettingChange("saturation", 130);
                onSettingChange("sharpness", 40);
                onSettingChange("denoise", 5);
              }}
            >
              {t("image_enhancer.preset_vivid")}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onSettingChange("brightness", 95);
                onSettingChange("contrast", 130);
                onSettingChange("saturation", 90);
                onSettingChange("sharpness", 30);
                onSettingChange("denoise", 15);
              }}
            >
              {t("image_enhancer.preset_dramatic")}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
