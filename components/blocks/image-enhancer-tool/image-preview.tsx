"use client";

import React, { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Icon from "@/components/icon";

interface EnhancementSettings {
  brightness: number;
  contrast: number;
  saturation: number;
  sharpness: number;
  denoise: number;
}

interface ImagePreviewProps {
  imageFile: File;
  settings: EnhancementSettings;
  canvasRef: React.RefObject<HTMLCanvasElement>;
}

export default function ImagePreview({ imageFile, settings, canvasRef }: ImagePreviewProps) {
  const t = useTranslations();
  const [showOriginal, setShowOriginal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const originalCanvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!imageFile || !canvasRef.current || !originalCanvasRef.current) return;

    const canvas = canvasRef.current;
    const originalCanvas = originalCanvasRef.current;
    const ctx = canvas.getContext("2d");
    const originalCtx = originalCanvas.getContext("2d");
    
    if (!ctx || !originalCtx) return;

    const img = new Image();
    img.onload = () => {
      // Set canvas dimensions
      const maxWidth = 800;
      const maxHeight = 600;
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;
      originalCanvas.width = width;
      originalCanvas.height = height;

      // Draw original image
      originalCtx.drawImage(img, 0, 0, width, height);
      
      // Apply enhancements
      applyEnhancements(ctx, originalCtx, width, height, settings);
      setImageLoaded(true);
    };

    img.src = URL.createObjectURL(imageFile);

    return () => {
      URL.revokeObjectURL(img.src);
    };
  }, [imageFile, settings, canvasRef]);

  const applyEnhancements = (
    ctx: CanvasRenderingContext2D,
    originalCtx: CanvasRenderingContext2D,
    width: number,
    height: number,
    settings: EnhancementSettings
  ) => {
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Get original image data
    const originalImageData = originalCtx.getImageData(0, 0, width, height);
    let imageData = new ImageData(
      new Uint8ClampedArray(originalImageData.data),
      width,
      height
    );

    // Apply denoise (simple blur)
    if (settings.denoise > 0) {
      imageData = applyDenoise(imageData, settings.denoise);
    }

    // Apply sharpening
    if (settings.sharpness > 0) {
      imageData = applySharpen(imageData, settings.sharpness);
    }

    // Apply brightness, contrast, saturation using CSS filters
    ctx.putImageData(imageData, 0, 0);
    
    const filters = [
      `brightness(${settings.brightness}%)`,
      `contrast(${settings.contrast}%)`,
      `saturate(${settings.saturation}%)`
    ].join(" ");

    ctx.filter = filters;
    ctx.drawImage(ctx.canvas, 0, 0);
    ctx.filter = "none";
  };

  const applyDenoise = (imageData: ImageData, strength: number): ImageData => {
    // Simple box blur for denoising
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const newData = new Uint8ClampedArray(data);
    
    const radius = Math.floor(strength / 20);
    if (radius < 1) return imageData;

    for (let y = radius; y < height - radius; y++) {
      for (let x = radius; x < width - radius; x++) {
        let r = 0, g = 0, b = 0, count = 0;
        
        for (let dy = -radius; dy <= radius; dy++) {
          for (let dx = -radius; dx <= radius; dx++) {
            const idx = ((y + dy) * width + (x + dx)) * 4;
            r += data[idx];
            g += data[idx + 1];
            b += data[idx + 2];
            count++;
          }
        }
        
        const idx = (y * width + x) * 4;
        newData[idx] = r / count;
        newData[idx + 1] = g / count;
        newData[idx + 2] = b / count;
      }
    }
    
    return new ImageData(newData, width, height);
  };

  const applySharpen = (imageData: ImageData, strength: number): ImageData => {
    // Simple unsharp mask
    const data = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    const newData = new Uint8ClampedArray(data);
    
    const factor = strength / 100;
    const kernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ];

    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let r = 0, g = 0, b = 0;
        
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4;
            const weight = kernel[(ky + 1) * 3 + (kx + 1)];
            r += data[idx] * weight;
            g += data[idx + 1] * weight;
            b += data[idx + 2] * weight;
          }
        }
        
        const idx = (y * width + x) * 4;
        const originalR = data[idx];
        const originalG = data[idx + 1];
        const originalB = data[idx + 2];
        
        newData[idx] = Math.max(0, Math.min(255, originalR + (r - originalR) * factor));
        newData[idx + 1] = Math.max(0, Math.min(255, originalG + (g - originalG) * factor));
        newData[idx + 2] = Math.max(0, Math.min(255, originalB + (b - originalB) * factor));
      }
    }
    
    return new ImageData(newData, width, height);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{t("image_enhancer.preview_title")}</CardTitle>
          <div className="flex gap-2">
            <Button
              variant={showOriginal ? "outline" : "default"}
              size="sm"
              onClick={() => setShowOriginal(false)}
            >
              {t("image_enhancer.enhanced")}
            </Button>
            <Button
              variant={showOriginal ? "default" : "outline"}
              size="sm"
              onClick={() => setShowOriginal(true)}
            >
              {t("image_enhancer.original")}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <div className="relative bg-gray-50 rounded-lg overflow-hidden min-h-[400px] flex items-center justify-center">
          {!imageLoaded && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Icon name="HiOutlineArrowPath" className="w-5 h-5 animate-spin" />
              {t("image_enhancer.loading")}
            </div>
          )}
          
          <canvas
            ref={canvasRef}
            className={`max-w-full max-h-[500px] ${showOriginal ? "hidden" : "block"}`}
            style={{ display: showOriginal ? "none" : "block" }}
          />
          
          <canvas
            ref={originalCanvasRef}
            className={`max-w-full max-h-[500px] ${showOriginal ? "block" : "hidden"}`}
            style={{ display: showOriginal ? "block" : "none" }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
