{"metadata": {"title": "Circle Crop Image - Free Online Circle Image Cropper", "description": "Free online tool to crop images into perfect circles. Privacy-focused with local processing - your images never leave your browser.", "keywords": "circle crop, image crop, round image, avatar crop, profile picture, circular image", "image_enhancer": {"title": "Image Enhancer - Free Online Image Quality Improvement Tool", "description": "Enhance image quality online for free. Improve brightness, contrast, sharpness, and reduce noise. Professional image enhancement in your browser.", "keywords": "image enhancer, improve image quality, image enhancement, photo enhancer, image quality, denoise, sharpen"}, "remove_background": {"title": "Remove Image Background - Free Background Remover Online", "description": "Remove background from any image free online. Professional AI-powered tool to automatically remove backgrounds. No registration, 100% private.", "keywords": "remove background, background remover, remove image background, transparent background, background removal, AI background remover"}}, "user": {"sign_in": "Sign In", "sign_out": "Sign Out", "credits": "Credits", "api_keys": "API Keys", "my_orders": "My Orders", "user_center": "User Center", "admin_system": "Admin System"}, "sign_modal": {"sign_in_title": "Sign In", "sign_in_description": "Sign in to your account", "sign_up_title": "Sign Up", "sign_up_description": "Create an account", "email_title": "Email", "email_placeholder": "Input your email here", "password_title": "Password", "password_placeholder": "Input your password here", "forgot_password": "Forgot password?", "or": "Or", "continue": "Continue", "no_account": "Don't have an account?", "email_sign_in": "Sign in with <PERSON><PERSON>", "google_sign_in": "Sign in with Google", "github_sign_in": "Sign in with GitHub", "close_title": "Close", "cancel_title": "Cancel"}, "my_orders": {"title": "My Orders", "description": "orders paid with ShipAny.", "no_orders": "No orders found", "tip": "", "activate_order": "Activate Order", "actived": "Activated", "join_discord": "Join <PERSON>", "read_docs": "Read Docs", "table": {"order_no": "Order No", "email": "Email", "product_name": "Product Name", "amount": "Amount", "paid_at": "<PERSON><PERSON>", "github_username": "GitHub Username", "status": "Status"}}, "my_credits": {"title": "My Credits", "left_tip": "left credits: {left_credits}", "no_credits": "No credits records", "recharge": "Recharge", "table": {"trans_no": "Trans No", "trans_type": "Trans Type", "credits": "Credits", "updated_at": "Updated At", "status": "Status"}}, "api_keys": {"title": "API Keys", "tip": "Please keep your apikey safe to avoid leaks", "no_api_keys": "No API Keys", "create_api_key": "Create API Key", "table": {"name": "Name", "key": "Key", "created_at": "Created At"}, "form": {"name": "Name", "name_placeholder": "API Key Name", "submit": "Submit"}}, "blog": {"title": "Blog", "description": "News, resources, and updates about ShipAny", "read_more_text": "Read More"}, "my_invites": {"title": "My Invites", "description": "View your invite records", "no_invites": "No invite records found", "my_invite_link": "My Invite Link", "edit_invite_link": "Edit Invite <PERSON>", "copy_invite_link": "Copy Invite Link", "invite_code": "Invite Code", "invite_tip": "Invite 1 friend to buy ShipAny, reward $50.", "invite_balance": "In<PERSON>te <PERSON>", "total_invite_count": "Total Invite Count", "total_paid_count": "Total Paid Count", "total_award_amount": "Total Award Amount", "update_invite_code": "Set Invite Code", "update_invite_code_tip": "Input your custom invite code", "update_invite_button": "Save", "no_orders": "You can't invite others before you bought ShipAny", "no_affiliates": "You're not allowed to invite others, please contact us to apply for permission.", "table": {"invite_time": "Invite Time", "invite_user": "Invite User", "status": "Status", "reward_percent": "<PERSON><PERSON> Percent", "reward_amount": "<PERSON><PERSON> Amount", "pending": "Pending", "completed": "Completed"}}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "description": "We'd love to hear what went well or how we can improve the product experience.", "submit": "Submit", "loading": "Submitting...", "contact_tip": "Other ways to contact us", "rating_tip": "How do you feel about <PERSON><PERSON><PERSON>?", "placeholder": "Leave your words here..."}, "upload": {"name": "upload", "title": "Upload Image", "title_active": "Drop your image here", "description": "Drag and drop your image here, or click to select", "formats": "Supports JPG, PNG, WEBP, GIF up to {maxSize}MB", "button": "Choose <PERSON>"}, "tool": {"steps": {"upload": "Upload", "crop": "Crop", "download": "Download"}, "titles": {"upload": "Upload Your Image", "crop": "Crop Your Image", "download": "Download Your Image"}, "features": {"privacy": {"title": "Completely Free", "description": "No signup or payment required"}, "quality": {"title": "High Quality", "description": "Original quality with transparent background"}, "security": {"title": "Your images never leave your browser", "description": "Complete privacy and security"}}, "preview": {"original": "Original", "cropped": "Cropped"}}, "usage_guide": {"pro_tips_title": "Pro Tips for Perfect Circle Crops", "tips": {"center_subject": {"title": "Center Your Subject", "description": "For the best results, choose images where the main subject is centered or can be positioned in the center of the frame."}, "high_resolution": {"title": "Use High-Resolution Images", "description": "Starting with high-quality images will result in better circular crops. Low-resolution images may appear pixelated."}, "mind_crop_area": {"title": "Mind the Crop Area", "description": "Remember that the corners of your image will be cropped out. Avoid images where important details are near the edges."}, "consistent_sets": {"title": "Create Consistent Sets", "description": "When creating multiple circular images, try to maintain consistent framing and sizes for a cohesive appearance."}}}, "features_showcase": {"comparison_title": "Why Choose Our Circle Crop Image Tool?", "our_tool": "Our Tool", "other_tools": "Other Tools", "our_features": {"0": "Instant processing - crop image to circle shape in seconds", "1": "Perfect circles every time with clean edges", "2": "Intuitive controls for easy positioning", "3": "No watermarks on your images", "4": "Works with all image types (JPG, PNG, WEBP, GIF)"}, "other_features": {"0": "Slow processing with server uploads", "1": "Imperfect circles with rough edges", "2": "Complex interfaces requiring tutorials", "3": "Watermarks added to free versions", "4": "Limited file format support"}}, "image_tips": {"common_uses_title": "Common Uses for Circle Cropped Images", "uses": {"social_media": {"title": "Social Media Profiles", "description": "Most platforms display avatars in circles - a perfectly cropped circular image looks professional"}, "team_photos": {"title": "Team Member Photos", "description": "Create consistent round headshots for company websites and directories"}, "portfolios": {"title": "Online Portfolios", "description": "Round images create visual interest and modern aesthetics"}, "business_cards": {"title": "Digital Business Cards", "description": "Circle-cropped profile photos look cleaner and more professional"}}}, "categories": {"preparation": "Preparation", "cropping": "Cropping", "general": "General"}, "crop": {"zoom": "Zoom", "processing": "Processing...", "crop_download": "Crop & Download", "instructions": {"drag": "Drag the image to position it within the circle.", "zoom": "Use the zoom slider to resize the image."}}, "download": {"title": "Image Ready!", "alt_text": "Cropped circle image", "format": "PNG format with transparent background", "size": "Size: {size}", "downloading": "Downloading...", "download_button": "Download Image", "copy": "Copy", "share": "Share", "crop_another": "Crop Another Image", "tip_label": "Tip", "tip_text": "The image has a transparent background", "perfect_for": "Perfect for websites, documents, and designs!"}, "image_enhancer": {"tool_title": "Upload Your Image to Enhance", "tool_description": "Upload any image and enhance its quality with professional tools. Adjust brightness, contrast, sharpness, and more.", "enhance_title": "Enhance Your Image", "enhance_description": "Use the controls on the right to adjust your image settings and see real-time results.", "preview_title": "Image Preview", "enhanced": "Enhanced", "original": "Original", "loading": "Loading...", "controls_title": "Enhancement Controls", "brightness": "Brightness", "contrast": "Contrast", "saturation": "Saturation", "sharpness": "Sharpness", "denoise": "<PERSON><PERSON>", "reset": "Reset", "presets": "Quick Presets", "preset_enhance": "<PERSON><PERSON>ce", "preset_soft": "Soft", "preset_vivid": "Vivid", "preset_dramatic": "Dramatic", "processing": "Processing...", "download_button": "Download Enhanced Image", "new_image": "Upload New Image", "tips_title": "Pro Tips", "tip_1": "Start with small adjustments for natural results", "tip_2": "Use presets as starting points for your enhancements", "tip_3": "Compare with original to see your improvements", "no_image_error": "No image to download", "download_success": "Image downloaded successfully!", "download_error": "Failed to download image"}, "remove_background": {"tool_title": "Remove Image Background", "tool_description": "Upload an image and our advanced algorithms will automatically remove the background", "processing_title": "Processing Your Image", "processing_description": "We're applying multiple methods to get the best results", "original_image": "Original Image", "new_image": "New Image", "results_title": "Processing Results", "results_description": "Choose the result you like best and download it", "method_color_filter": "Color Filter", "method_edge_detection": "Edge Detection", "method_advanced_analysis": "Advanced Analysis", "method_wasm_ai_model": "WebAssembly AI Model (Premium)", "method_ai_model": "AI Model", "method_color_filter_desc": "Ideal for uniform color backgrounds", "method_edge_detection_desc": "Best for backgrounds with simple patterns", "method_advanced_analysis_desc": "Smart analysis of dominant colors", "method_wasm_ai_model_desc": "Premium WebAssembly AI with highest quality results", "method_ai_model_desc": "Advanced AI for complex backgrounds and people", "processing": "Processing", "completed": "Completed", "error": "Error", "download": "Download", "processing_method": "Processing method...", "processing_all_methods": "Processing all methods...", "download_success": "Image downloaded successfully!", "download_error": "Failed to download image", "tip_title": "Tip:", "tip_description": "For best results, use images with good lighting and clear contrast between the subject and background.", "advanced_options": "Advanced Options", "color_threshold": "Color Threshold", "color_tolerance": "Color Tolerance", "edge_threshold": "Edge Threshold", "background_threshold": "<PERSON> Threshold", "segmentation_threshold": "Segmentation Threshold", "enable_post_processing": "Enable Post Processing", "reprocess": "Reprocess with New Settings", "processing_error": "Processing failed. Please try again."}, "common": {"show": "Show", "hide": "<PERSON>de"}}