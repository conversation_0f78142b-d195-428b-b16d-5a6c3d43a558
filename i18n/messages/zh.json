{"metadata": {"title": "Circle Crop Image - 免费在线圆形图片裁剪工具", "description": "免费在线圆形图片裁剪工具，注重隐私保护，本地处理 - 您的图片不会离开浏览器。", "keywords": "圆形裁剪, 图片裁剪, 圆形图片, 头像裁剪, 个人资料图片, 圆形图像", "image_enhancer": {"title": "图像增强器 - 免费在线图像质量提升工具", "description": "免费在线增强图像质量。提升亮度、对比度、锐度，减少噪点。浏览器内专业图像增强。", "keywords": "图像增强, 提升图像质量, 图像增强, 照片增强器, 图像质量, 去噪, 锐化"}, "remove_background": {"title": "去除图像背景 - 免费在线背景去除工具", "description": "免费在线去除任何图像背景。AI驱动的专业工具自动去除背景。无需注册，100%私密。", "keywords": "去除背景, 背景去除器, 去除图像背景, 透明背景, 背景去除, AI背景去除器"}}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "upload": {"name": "upload", "title": "上传图片", "title_active": "将图片拖放到这里", "description": "将图片拖放到这里，或点击选择文件", "formats": "支持 JPG、PNG、WEBP、GIF 格式，最大 {maxSize}MB", "button": "选择文件"}, "tool": {"steps": {"upload": "上传", "crop": "裁剪", "download": "下载"}, "titles": {"upload": "上传您的图片", "crop": "裁剪您的图片", "download": "下载您的图片"}, "features": {"privacy": {"title": "私密安全", "description": "无需注册或付费"}, "quality": {"title": "高质量", "description": "保持原始质量，透明背景"}, "security": {"title": "图片不会离开您的浏览器", "description": "完全隐私和安全"}}, "preview": {"original": "原图", "cropped": "裁剪后"}}, "usage_guide": {"pro_tips_title": "完美圆形裁剪的专业技巧", "tips": {"center_subject": {"title": "居中主要对象", "description": "为了获得最佳效果，请选择主要对象居中或可以定位在画面中心的图片。"}, "high_resolution": {"title": "使用高分辨率图片", "description": "从高质量的图片开始，可以获得更好的圆形裁剪效果。低分辨率图片可能会出现像素化。"}, "mind_crop_area": {"title": "注意裁剪区域", "description": "请记住图片的四个角落会被裁剪掉。避免在边缘附近放置重要细节。"}, "consistent_sets": {"title": "创建一致的图片集", "description": "创建多个圆形图片时，尽量保持一致的构图和尺寸，以获得协调的外观。"}}}, "features_showcase": {"comparison_title": "为什么选择我们的圆形裁剪图片工具？", "our_tool": "我们的工具", "other_tools": "其他工具", "our_features": {"0": "即时处理 - 几秒钟内将图片裁剪成圆形", "1": "每次都能获得完美的圆形，边缘清晰", "2": "直观的控制，轻松定位", "3": "您的图片上没有水印", "4": "支持所有图片类型（JPG、PNG、WEBP、GIF）"}, "other_features": {"0": "服务器上传处理缓慢", "1": "圆形不完美，边缘粗糙", "2": "复杂的界面需要教程", "3": "免费版本添加水印", "4": "文件格式支持有限"}}, "image_tips": {"common_uses_title": "圆形裁剪图片的常见用途", "uses": {"social_media": {"title": "社交媒体头像", "description": "大多数平台都以圆形显示头像 - 完美裁剪的圆形图片看起来更专业"}, "team_photos": {"title": "团队成员照片", "description": "为公司网站和目录创建一致的圆形头像"}, "portfolios": {"title": "在线作品集", "description": "圆形图片创造视觉趣味和现代美感"}, "business_cards": {"title": "数字名片", "description": "圆形裁剪的个人资料照片看起来更干净、更专业"}}}, "categories": {"preparation": "准备工作", "cropping": "裁剪过程", "general": "常规"}, "crop": {"zoom": "缩放", "processing": "处理中...", "crop_download": "裁剪并下载", "instructions": {"drag": "拖动图片在圆形区域内定位。", "zoom": "使用缩放滑块调整图片大小。"}}, "download": {"title": "图片已准备好！", "alt_text": "裁剪后的圆形图片", "format": "PNG格式，透明背景", "size": "大小：{size}", "downloading": "下载中...", "download_button": "下载图片", "copy": "复制", "share": "分享", "crop_another": "裁剪另一张图片", "tip_label": "提示", "tip_text": "图片具有透明背景", "perfect_for": "非常适合网站、文档和设计！"}, "image_enhancer": {"tool_title": "上传图片进行增强", "tool_description": "上传任意图片，使用专业工具增强其质量。调整亮度、对比度、锐度等。", "enhance_title": "增强您的图片", "enhance_description": "使用右侧的控制面板调整图片设置，实时查看效果。", "preview_title": "图片预览", "enhanced": "增强后", "original": "原图", "loading": "加载中...", "controls_title": "增强控制", "brightness": "亮度", "contrast": "对比度", "saturation": "饱和度", "sharpness": "锐化", "denoise": "去噪", "reset": "重置", "presets": "快速预设", "preset_enhance": "增强", "preset_soft": "柔和", "preset_vivid": "鲜艳", "preset_dramatic": "戏剧化", "processing": "处理中...", "download_button": "下载增强图片", "new_image": "上传新图片", "tips_title": "专业提示", "tip_1": "从小幅调整开始，获得自然效果", "tip_2": "使用预设作为增强的起点", "tip_3": "与原图对比查看改进效果", "no_image_error": "没有图片可下载", "download_success": "图片下载成功！", "download_error": "图片下载失败"}, "remove_background": {"tool_title": "图像背景去除", "tool_description": "上传图像，我们的先进算法将自动去除背景", "processing_title": "正在处理您的图像", "processing_description": "我们正在应用多种方法以获得最佳效果", "original_image": "原始图像", "new_image": "新图像", "results_title": "处理结果", "results_description": "选择您最喜欢的结果并下载", "method_color_filter": "颜色过滤", "method_edge_detection": "边缘检测", "method_advanced_analysis": "高级分析", "method_ai_model": "AI模型", "method_color_filter_desc": "适用于单一颜色背景", "method_edge_detection_desc": "适用于简单图案背景", "method_advanced_analysis_desc": "智能分析主导颜色", "method_ai_model_desc": "先进AI处理复杂背景和人物", "processing": "处理中", "completed": "已完成", "error": "错误", "download": "下载", "processing_method": "正在处理方法...", "processing_all_methods": "正在处理所有方法...", "download_success": "图像下载成功！", "download_error": "图像下载失败", "tip_title": "提示：", "tip_description": "为获得最佳效果，请使用光线良好且主体与背景对比清晰的图像。", "advanced_options": "高级选项", "color_threshold": "颜色阈值", "color_tolerance": "颜色容差", "edge_threshold": "边缘阈值", "background_threshold": "背景阈值", "segmentation_threshold": "分割阈值", "enable_post_processing": "启用后处理", "reprocess": "使用新设置重新处理", "processing_error": "处理失败，请重试。"}, "common": {"show": "显示", "hide": "隐藏"}}