import { Pathnames } from "next-intl/routing";

export const locales = ["en", "zh", "es", "fr", "de", "ja", "ko", "pt", "ru", "it", "ar", "hi"];

export const localeNames: any = {
  en: "English",
  zh: "中文",
  es: "Español",
  fr: "Français",
  de: "Deutsch",
  ja: "日本語",
  ko: "한국어",
  pt: "Português",
  ru: "Русский",
  it: "Italiano",
  ar: "العربية",
  hi: "हिन्दी",
};

export const defaultLocale = "en";

export const localePrefix = "as-needed";

export const localeDetection =
  process.env.NEXT_PUBLIC_LOCALE_DETECTION === "true";

export const pathnames = {
  "/": "/",
  "/privacy-policy": {
    en: "/privacy-policy",
    zh: "/privacy-policy",
    es: "/privacy-policy",
    fr: "/privacy-policy",
    de: "/privacy-policy",
    ja: "/privacy-policy",
    ko: "/privacy-policy",
    pt: "/privacy-policy",
    ru: "/privacy-policy",
    it: "/privacy-policy",
    ar: "/privacy-policy",
    hi: "/privacy-policy",
  },
  "/terms-of-service": {
    en: "/terms-of-service",
    zh: "/terms-of-service",
    es: "/terms-of-service",
    fr: "/terms-of-service",
    de: "/terms-of-service",
    ja: "/terms-of-service",
    ko: "/terms-of-service",
    pt: "/terms-of-service",
    ru: "/terms-of-service",
    it: "/terms-of-service",
    ar: "/terms-of-service",
    hi: "/terms-of-service",
  },
} satisfies Pathnames<typeof locales>;
