{"template": "circlecrop-template", "theme": "light", "header": {"brand": {"title": "원형 이미지 자르기", "logo": {"src": "/logo.svg", "alt": "원형 이미지 자르기"}, "url": "/ko"}, "nav": {"items": [{"title": "기능", "url": "/#feature", "icon": "HiOutlineSparkles"}, {"title": "사용법", "url": "/#usage", "icon": "HiOutlineQuestionMarkCircle"}, {"title": "FAQ", "url": "/#faq", "icon": "HiOutlineChatBubbleLeftRight"}]}, "buttons": [], "show_sign": false, "show_theme": true, "show_locale": true}, "hero": {"title": "무료 온라인 원형 이미지 자르기 도구", "highlight_text": "원형 자르기", "description": "어떤 이미지든 즉시 완벽한 원형으로 변환하세요. 프로필 사진, 소셜 미디어, 디자인 프로젝트에 이상적입니다. 가입 불필요.", "buttons": [{"title": "이미지 업로드", "icon": "HiOutlineCloudArrowUp", "url": "#upload", "target": "_self", "variant": "default"}], "tip": "✨ 무료 • 가입 불필요 • 개인정보 보호", "show_happy_users": false, "show_badge": false}, "upload": {"name": "upload", "title": "이미지 업로드", "title_active": "여기에 이미지를 드롭하세요", "description": "이미지를 여기에 드래그 앤 드롭하거나 클릭하여 선택하세요", "formats": "JPG, PNG, WEBP, GIF 최대 {maxSize}MB 지원", "button": "파일 선택"}, "cta": {"name": "cta", "title": "완벽한 원형 이미지 만들기 시작", "description": "사진을 즉시 전문적인 원형 이미지로 변환하세요.", "buttons": [{"title": "이미지 업로드", "url": "#upload", "target": "_self", "icon": "HiOutlineCloudArrowUp"}]}, "branding": {"title": "Circle Crop Image는 거인의 어깨 위에 구축되었습니다", "items": [{"title": "Next.js", "image": {"src": "/imgs/logos/nextjs.svg", "alt": "Next.js"}}, {"title": "React", "image": {"src": "/imgs/logos/react.svg", "alt": "React"}}, {"title": "TailwindCSS", "image": {"src": "/imgs/logos/tailwindcss.svg", "alt": "TailwindCSS"}}, {"title": "Shadcn/UI", "image": {"src": "/imgs/logos/shadcn.svg", "alt": "Shadcn/UI"}}, {"title": "Vercel", "image": {"src": "/imgs/logos/vercel.svg", "alt": "Vercel"}}]}, "introduce": {"name": "introduce", "title": "원형 이미지 자르기 도구", "label": "소개", "description": "Circle Crop Image는 직사각형 이미지를 완벽한 원형으로 변환하는 웹 최고의 무료 온라인 도구입니다. 우리 도구는 가입 없이 즉시 원형 형식으로 이미지를 자르는 데 도움이 됩니다.", "items": [{"title": "이미지 업로드", "description": "원형으로 자르고 싶은 기기의 사진을 선택하세요.", "icon": "HiOutlineCloudArrowUp"}, {"title": "이미지 위치 조정", "description": "완벽한 원형 자르기를 위해 원형 자르기 영역 내에서 사진을 조정하고 중앙에 배치하세요.", "icon": "HiOutlineAdjustmentsHorizontal"}, {"title": "원형 이미지 다운로드", "description": "어디서나 사용할 수 있는 완벽하게 둥근 이미지를 얻으세요.", "icon": "HiOutlineArrowDownTray"}]}, "usage": {"name": "usage", "title": "이미지를 원형으로 자르는 방법", "description": "세 가지 간단한 단계로 모든 이미지를 완벽한 원으로 변환:", "text_align": "center", "items": [{"title": "이미지 업로드", "description": "원형으로 자르고 싶은 기기의 사진을 선택하세요.", "icon": "HiOutlineCloudArrowUp"}, {"title": "이미지 위치 조정", "description": "완벽한 원형 자르기를 위해 원형 자르기 영역 내에서 사진을 조정하고 중앙에 배치하세요.", "icon": "HiOutlineAdjustmentsHorizontal"}, {"title": "원형 이미지 다운로드", "description": "어디서나 사용할 수 있는 완벽하게 둥근 이미지를 얻으세요.", "icon": "HiOutlineArrowDownTray"}]}, "feature": {"name": "feature", "title": "원형 자르기 도구의 기능", "description": "완벽한 원형 이미지를 빠르고 쉽게 만들기 위해 필요한 모든 것.", "items": [{"title": "무료 원형 이미지 자르기", "description": "모든 이미지에서 원형을 무료로 만드세요.", "icon": "HiOutlineGift"}, {"title": "고해상도 출력", "description": "원형 이미지를 원본 품질로 다운로드하세요.", "icon": "HiOutlinePhoto"}, {"title": "가입 불필요", "description": "원형 이미지 자르기를 즉시 시작하세요.", "icon": "HiOutlineBoltSlash"}, {"title": "비공개 및 보안", "description": "이미지가 브라우저를 떠나지 않습니다.", "icon": "HiOutlineShieldCheck"}, {"title": "사용하기 쉬운 인터페이스", "description": "간단한 디자인으로 둥근 이미지 만들기가 쉽습니다.", "icon": "HiOutlineCursorArrowRays"}, {"title": "모든 기기에서 작동", "description": "데스크톱, 태블릿, 모바일에서 원형 이미지 자르기.", "icon": "HiOutlineDevicePhoneMobile"}]}, "faq": {"name": "faq", "label": "FAQ", "title": "원형 자르기 이미지 FAQ", "description": "무료 원형 자르기 도구에 대한 일반적인 질문.", "items": [{"title": "이미지를 원형으로 자르려면 어떻게 해야 하나요?", "description": "이미지를 도구에 업로드하고, 피사체를 적절히 배치하기 위해 원형 프레임 내에서 조정한 다음, '자르기 및 다운로드'를 클릭하여 완벽하게 둥근 이미지를 얻으세요."}, {"title": "원형 프로필 사진을 만들려면 어떻게 해야 하나요?", "description": "사진을 원형 자르기 도구에 업로드하고, 원형 프레임 중앙에 얼굴을 배치한 다음, 새로운 둥근 프로필 사진을 다운로드하세요."}, {"title": "휴대폰에서 사진을 둥근 모양으로 자를 수 있나요?", "description": "네! 원형 이미지 자르기는 스마트폰과 태블릿을 포함한 모든 기기에서 작동합니다. 모바일 친화적인 인터페이스로 어디서나 쉽게 둥근 이미지 자르기를 만들 수 있습니다."}, {"title": "업로드한 이미지를 저장하나요?", "description": "아니요, 서버에 이미지를 저장하지 않습니다. 전체 자르기 과정이 브라우저에서 직접 이루어지므로 사진이 비공개로 유지됩니다."}, {"title": "원형 자르기 도구에서 어떤 이미지 형식을 사용할 수 있나요?", "description": "도구는 JPG, PNG, WEBP, GIF를 포함한 모든 표준 이미지 형식에서 작동합니다. 이러한 형식 중 하나를 업로드할 수 있으며, 원형 자른 이미지는 원 주위에 투명도가 있는 PNG 파일로 다운로드됩니다."}, {"title": "원형 자른 이미지에 투명한 배경이 있나요?", "description": "네, 자른 이미지를 다운로드하면 원 주위에 투명한 배경이 있습니다. 이는 원형 부분만 보이게 하려는 웹사이트, 문서 또는 디자인에 배치하기에 완벽합니다."}]}, "tips": {"name": "tips", "title": "완벽한 원형 자르기를 위한 이미지 팁", "description": "원형 이미지 자르기에서 최상의 결과를 얻기 위한 전문가 팁을 따르세요", "items": [{"title": "고해상도 이미지 사용", "description": "원형 자르기가 선명하고 명확하게 유지되도록 고품질 원본 이미지로 시작하세요.", "icon": "HiOutlinePhoto", "category": "preparation", "importance": "high"}, {"title": "피사체를 중앙에 배치", "description": "최적의 원형 자르기를 위해 이미지의 주요 초점(얼굴 등)이 중앙에 위치하는지 확인하세요.", "icon": "HiOutlineEye", "category": "preparation", "importance": "high"}, {"title": "배경 대비 고려", "description": "원형 가장자리를 돋보이게 하기 위해 피사체와 잘 대비되는 배경을 선택하세요.", "icon": "HiOutlineAdjustmentsHorizontal", "category": "preparation", "importance": "medium"}, {"title": "정사각형 비율이 최적", "description": "정사각형 이미지가 원형 자르기에 가장 적합하지만, 도구는 모든 비율을 처리할 수 있습니다.", "icon": "HiOutlineSquare3Stack3D", "category": "preparation", "importance": "medium"}, {"title": "신중하게 배치", "description": "이미지의 중요한 부분이 원 안에 있는지 확인하기 위해 배치 컨트롤을 사용하세요.", "icon": "HiOutlineArrowsPointingOut", "category": "cropping", "importance": "high"}, {"title": "다운로드 전 미리보기", "description": "완벽하게 보이는지 확인하기 위해 다운로드 전에 항상 원형 자르기를 미리보세요.", "icon": "HiOutlineEye", "category": "cropping", "importance": "medium"}]}, "footer": {"name": "footer", "brand": {"title": "Circle Crop Image", "description": "모든 이미지를 완벽한 원으로 자르는 무료 온라인 도구 - 프로필 사진, 소셜 미디어, 디자인 프로젝트에 이상적.", "logo": {"src": "/logo.svg", "alt": "Circle Crop Image"}, "url": "/ko"}, "copyright": "© 2025 Circle Crop Image. 모든 권리 보유.", "nav": {"items": [{"title": "도구", "children": [{"title": "원형 자르기", "url": "/#upload", "target": "_self"}, {"title": "기능", "url": "/#feature", "target": "_self"}, {"title": "사용법", "url": "/#usage", "target": "_self"}]}, {"title": "지원", "children": [{"title": "FAQ", "url": "/#faq", "target": "_self"}]}]}, "agreement": {"items": [{"title": "개인정보 보호정책", "url": "/privacy-policy"}, {"title": "이용약관", "url": "/terms-of-service"}, {"title": "문의하기", "url": "/contact"}]}}}